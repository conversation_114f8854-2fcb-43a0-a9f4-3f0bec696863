#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration file for button types in the teacher extraction process
Updated with verified working selectors from button testing
"""

# Button types to extract data from
BUTTON_TYPES = [
    "詳細",      # Details
    "報酬設定",  # Compensation settings
    "署名"       # Signature
]

# Button configuration
BUTTON_CONFIG = {
    "詳細": {
        "selectors": [
            "//input[@value='詳細']",
        ],
        "back_selectors": [
            "//input[@type='button' and contains(@value, '戻る')]",
        ],
        "use_browser_back": False
    },
    "報酬設定": {
        "selectors": [
            "//input[@value='報酬設定']",
        ],
        "back_selectors": [
            "//input[@type='button' and contains(@value, '戻る')]",
        ],
        "use_browser_back": False
    },
    "署名": {
        "selectors": [
            "//input[@value='署名']",
        ],
        "back_selectors": [
            "//input[@type='button' and contains(@value, '戻る')]",
        ],
        "use_browser_back": False
    }
}

# Details about what data to extract from each page
DATA_EXTRACTION_CONFIG = {
    "詳細": {
        "tables": True,  # Extract table data
        "forms": True,   # Extract form field data
        "key_fields": [  # List of important field names or IDs to look for
            "name", "email", "phone", "address", "status", "category", "type"
        ]
    },
    "報酬設定": {
        "tables": True,
        "forms": True,
        "key_fields": [
            "payment", "amount", "compensation", "payment_method"
        ]
    },
    "署名": {
        "tables": True,
        "forms": True,
        "key_fields": [
            "signature", "date", "document"
        ]
    }
}

# This dictionary contains the verified configuration from testing
VERIFIED_BUTTON_CONFIG = {
    "詳細": {
        "selector": "//input[@value='詳細']",
        "back_selector": "//input[@type='button' and contains(@value, '戻る')]",
        "use_browser_back": False
    },
    "報酬設定": {
        "selector": "//input[@value='報酬設定']",
        "back_selector": "//input[@type='button' and contains(@value, '戻る')]",
        "use_browser_back": False
    },
    "署名": {
        "selector": "//input[@value='署名']",
        "back_selector": "//input[@type='button' and contains(@value, '戻る')]",
        "use_browser_back": False
    }
}
