# logger.py
import os
import logging
from datetime import datetime
from config import LOGS_DIR

def setup_logger():
    """
    Thiết lập logging
    """
    # Tạo tên file log với timestamp
    log_filename = os.path.join(LOGS_DIR, f"scraping_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # Cấ<PERSON> h<PERSON>nh logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger()

# Khởi tạo logger
logger = setup_logger()
