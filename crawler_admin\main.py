# main.py
import os
import sys
import time
import traceback
from datetime import datetime
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Import các module tự tạo
from config import (
    LOGIN_URL, DATA_URL, USERNAME, PASSWORD, 
    MAIN_PAGE_COLUMNS, INSTRUCTOR_PAGE_COLUMNS,
    HTML_DIR, SCREENSHOTS_DIR, DATA_DIR, INSTRUCTOR_DIR,
    PAGE_LOAD_WAIT, DEFAULT_TIMEOUT, get_timestamp
)
from logger import logger
from browser import init_driver
from login import login, access_page
from screenshot import take_full_page_screenshot
from data_extraction import (
    find_table, extract_table_data, 
    find_instructor_buttons, extract_room_id_from_onclick
)
from file_utils import save_html, save_data_files

def process_instructor_page(driver, index, room_id):
    """
    Xử lý trang インストラクター設定へ
    """
    logger.info(f"Đang xử lý trang インストラクター設定へ cho room_id={room_id} (thứ {index + 1})")
    
    try:
        # Tạo thư mục cho trang インストラクター設定へ
        instructor_folder = f"{INSTRUCTOR_DIR}/room_{room_id}"
        os.makedirs(instructor_folder, exist_ok=True)
        
        # Lưu HTML
        html_filename = save_html(driver, instructor_folder, f"instructor")
        
        # Chụp ảnh toàn trang
        screenshot_filename = f"{instructor_folder}/instructor_{get_timestamp()}.png"
        take_full_page_screenshot(driver, screenshot_filename)
        
        # Tìm bảng dữ liệu
        table = find_table(driver)
        if not table:
            logger.error("Không tìm thấy bảng dữ liệu trong trang インストラクター設定へ")
            return False
        
        # Trích xuất dữ liệu từ bảng
        headers, data = extract_table_data(table, INSTRUCTOR_PAGE_COLUMNS, is_instructor_page=True)
        if not data:
            logger.error("Không trích xuất được dữ liệu từ bảng trong trang インストラクター設定へ")
            return False
        
        # Lưu dữ liệu
        save_data_files(data, instructor_folder, "instructor_data")
        
        return True
    except Exception as e:
        logger.error(f"Lỗi khi xử lý trang インストラクター設定へ: {e}")
        traceback.print_exc()
        return False

def main():
    """
    Hàm chính thực hiện toàn bộ quy trình
    """
    logger.info("=== BẮT ĐẦU QUY TRÌNH TRÍCH XUẤT DỮ LIỆU ===")
    logger.info(f"Thời gian bắt đầu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    start_time = time.time()
    
    # Khởi tạo trình duyệt
    driver = None
    try:
        # Khởi tạo trình duyệt
        driver = init_driver()
        
        # Đăng nhập
        if not login(driver, LOGIN_URL, USERNAME, PASSWORD):
            logger.error("Không thể tiếp tục do đăng nhập thất bại")
            return
        
        # Thêm delay để đảm bảo đăng nhập hoàn tất
        time.sleep(PAGE_LOAD_WAIT)
        
        # Truy cập trang dữ liệu
        if not access_page(driver, DATA_URL):
            logger.error("Không thể tiếp tục do truy cập trang dữ liệu thất bại")
            return
        
        # Thêm delay để đảm bảo trang tải xong
        time.sleep(PAGE_LOAD_WAIT)
        
        # Lưu HTML của trang
        save_html(driver, HTML_DIR, "main_page")
        
        # Chụp ảnh toàn trang
        screenshot_filename = f"{SCREENSHOTS_DIR}/main_page_{get_timestamp()}.png"
        take_full_page_screenshot(driver, screenshot_filename)
        
        # Tìm bảng dữ liệu
        table = find_table(driver)
        if not table:
            logger.error("Không thể tiếp tục do không tìm thấy bảng dữ liệu")
            return
        
        # Trích xuất dữ liệu từ bảng
        headers, data = extract_table_data(table, MAIN_PAGE_COLUMNS)
        if not data:
            logger.error("Không thể tiếp tục do không trích xuất được dữ liệu")
            return
        
        # Lưu dữ liệu
        save_data_files(data, DATA_DIR, "main_page")
        
        # Lưu URL hiện tại để quay lại sau khi xử lý từng trang
        main_page_url = driver.current_url
        
        # Xử lý từng nút インストラクター設定へ
        i = 0
        while True:
            try:
                # Tìm lại các nút mỗi lần để tránh lỗi stale
                instructor_buttons = find_instructor_buttons(driver)
                if not instructor_buttons:
                    logger.error("Không tìm thấy nút インストラクター設定へ nào")
                    break
                
                # Kiểm tra nếu đã xử lý hết các nút
                if i >= len(instructor_buttons):
                    logger.info(f"Đã xử lý tất cả {len(instructor_buttons)} nút インストラクター設定へ")
                    break
                
                # Lấy nút hiện tại
                button = instructor_buttons[i]
                
                # Lấy room_id từ onclick
                onclick = button.get_attribute("onclick")
                room_id = extract_room_id_from_onclick(onclick)
                
                if not room_id:
                    logger.warning(f"Không thể trích xuất room_id từ nút thứ {i + 1}, bỏ qua")
                    i += 1
                    continue
                
                logger.info(f"Đang nhấn nút インストラクター設定へ thứ {i + 1}/{len(instructor_buttons)} (room_id={room_id})")
                
                try:
                    # Lưu tham chiếu đến trang hiện tại
                    old_page = driver.find_element(By.TAG_NAME, 'html')
                    
                    # Sử dụng JavaScript để nhấn nút để tránh lỗi khi nút không hiển thị
                    driver.execute_script("arguments[0].click();", button)
                    
                    # Đợi trang mới tải xong
                    WebDriverWait(driver, DEFAULT_TIMEOUT).until(EC.staleness_of(old_page))
                    
                    # Đợi thêm để trang mới tải hoàn tất
                    WebDriverWait(driver, DEFAULT_TIMEOUT).until(
                        EC.presence_of_element_located((By.TAG_NAME, 'body'))
                    )
                except TimeoutException:
                    logger.warning("Timeout khi chờ trang mới tải, thử tiếp tục xử lý")
                
                # Thêm delay để đảm bảo trang tải xong
                time.sleep(PAGE_LOAD_WAIT)
                
                # Xử lý trang インストラクター設定へ
                process_instructor_page(driver, i, room_id)
                
                # Quay lại trang chính
                driver.get(main_page_url)
                time.sleep(PAGE_LOAD_WAIT)
                
                # Tăng chỉ số để xử lý nút tiếp theo
                i += 1
                
            except StaleElementReferenceException:
                logger.error(f"Phần tử đã bị cũ (stale), đang tải lại trang và tìm lại các nút")
                driver.get(main_page_url)
                time.sleep(PAGE_LOAD_WAIT)
                # Không tăng i để thử lại nút hiện tại
            except Exception as e:
                logger.error(f"Lỗi khi xử lý nút インストラクター設定へ thứ {i + 1}: {e}")
                traceback.print_exc()
                # Quay lại trang chính để tiếp tục với nút tiếp theo
                driver.get(main_page_url)
                time.sleep(PAGE_LOAD_WAIT)
                i += 1  # Tăng i để bỏ qua nút gây lỗi
        
        elapsed_time = time.time() - start_time
        logger.info(f"=== KẾT THÚC QUY TRÌNH TRÍCH XUẤT DỮ LIỆU (thời gian: {elapsed_time:.2f} giây) ===")
    
    except Exception as e:
        logger.error(f"Lỗi không xác định: {e}")
        traceback.print_exc()
    
    finally:
        # Đảm bảo đóng trình duyệt
        if driver:
            driver.quit()
            logger.info("Đã đóng trình duyệt")

if __name__ == "__main__":
    main()
