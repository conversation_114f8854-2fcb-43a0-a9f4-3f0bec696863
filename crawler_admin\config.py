# config.py
import os
from datetime import datetime

# Thông tin đăng nhập
LOGIN_URL = "https://www.en-system.net/admin/"
DATA_URL = "https://www.en-system.net/admin/tool/online_main.php"
USERNAME = "hirano"  # Thay đổi thành username thực tế
PASSWORD = "hirano"  # Thay đổi thành password thực tế

# Cấu hình thư mục
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
LOGS_DIR = os.path.join(BASE_DIR, "logs")
DATA_DIR = os.path.join(BASE_DIR, "data")
SCREENSHOTS_DIR = os.path.join(BASE_DIR, "screenshots")
HTML_DIR = os.path.join(BASE_DIR, "html")
INSTRUCTOR_DIR = os.path.join(BASE_DIR, "インストラクター設定へ")

# <PERSON><PERSON><PERSON> thư mục nếu chưa tồn tại
for directory in [LOGS_DIR, DATA_DIR, SCREENSHOTS_DIR, HTML_DIR, INSTRUCTOR_DIR]:
    os.makedirs(directory, exist_ok=True)

# Cấu hình trình duyệt
HEADLESS = False  # True để chạy ẩn, False để hiển thị giao diện
BROWSER_WIDTH = 1920
BROWSER_HEIGHT = 1080

# Cấu hình timeout
DEFAULT_TIMEOUT = 20
PAGE_LOAD_WAIT = 3

# Cấu hình cột dữ liệu
MAIN_PAGE_COLUMNS = ["教室ID", "公開", "教室名", "一覧用URL"]
INSTRUCTOR_PAGE_COLUMNS = ["表示順", "公開", "インストラクター名", "種別", "カテゴリー", "性別", "Week ＆ Time(ホームページ表示用)"]

# Tạo timestamp
def get_timestamp():
    return datetime.now().strftime("%Y%m%d_%H%M%S")
