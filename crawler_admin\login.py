# login.py
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import traceback
from config import DEFAULT_TIMEOUT
from logger import logger
from screenshot import take_full_page_screenshot
from config import SCREENSHOTS_DIR, get_timestamp

def login(driver, url, username, password):
    """
    Đăng nhập vào trang web với tài khoản và mật khẩu
    """
    logger.info(f"Đang truy cập trang đăng nhập: {url}")
    try:
        driver.get(url)
        # Đợi trang đăng nhập tải xong
        WebDriverWait(driver, DEFAULT_TIMEOUT).until(
            EC.presence_of_element_located((By.TAG_NAME, 'body'))
        )
        
        # <PERSON><PERSON><PERSON>nh chụp màn hình trang đăng nhập
        screenshot_path = f'{SCREENSHOTS_DIR}/login_page_{get_timestamp()}.png'
        take_full_page_screenshot(driver, screenshot_path)
        
        # Tìm form đăng nhập
        try:
            # Thử tìm trường username theo name
            username_input = WebDriverWait(driver, DEFAULT_TIMEOUT).until(
                EC.presence_of_element_located((By.NAME, 'username'))
            )
            logger.info("Đã tìm thấy trường username")
        except TimeoutException:
            # Thử tìm theo các thuộc tính khác
            username_selectors = [
                (By.ID, 'username'),
                (By.ID, 'user'),
                (By.ID, 'email'),
                (By.NAME, 'user'),
                (By.NAME, 'email'),
                (By.NAME, 'userid'),
                (By.XPATH, '//input[@type="text"]'),
                (By.XPATH, '//input[contains(@placeholder, "user") or contains(@placeholder, "name") or contains(@placeholder, "email")]')
            ]
            
            for selector_type, selector in username_selectors:
                try:
                    username_input = driver.find_element(selector_type, selector)
                    logger.info(f"Đã tìm thấy trường username với selector: {selector_type}={selector}")
                    break
                except NoSuchElementException:
                    continue
            else:
                raise NoSuchElementException("Không tìm thấy trường username")
        
        # Tìm trường password
        try:
            password_input = WebDriverWait(driver, DEFAULT_TIMEOUT).until(
                EC.presence_of_element_located((By.NAME, 'password'))
            )
            logger.info("Đã tìm thấy trường password")
        except TimeoutException:
            # Thử tìm theo các thuộc tính khác
            password_selectors = [
                (By.ID, 'password'),
                (By.ID, 'pass'),
                (By.ID, 'pwd'),
                (By.NAME, 'pass'),
                (By.NAME, 'pwd'),
                (By.XPATH, '//input[@type="password"]')
            ]
            
            for selector_type, selector in password_selectors:
                try:
                    password_input = driver.find_element(selector_type, selector)
                    logger.info(f"Đã tìm thấy trường password với selector: {selector_type}={selector}")
                    break
                except NoSuchElementException:
                    continue
            else:
                raise NoSuchElementException("Không tìm thấy trường password")
        
        # Nhập thông tin đăng nhập
        username_input.clear()
        username_input.send_keys(username)
        logger.info("Đã nhập username")
        
        password_input.clear()
        password_input.send_keys(password)
        logger.info("Đã nhập password")
        
        # Tìm nút đăng nhập
        submit_selectors = [
            (By.XPATH, '//button[@type="submit"]'),
            (By.XPATH, '//input[@type="submit"]'),
            (By.XPATH, '//button[contains(text(), "Login") or contains(text(), "Sign in") or contains(text(), "ログイン")]'),
            (By.XPATH, '//input[@value="Login" or @value="Sign in" or @value="ログイン")]'),
            (By.CSS_SELECTOR, 'form button'),
            (By.CSS_SELECTOR, 'form input[type="submit"]')
        ]
        
        for selector_type, selector in submit_selectors:
            try:
                submit_button = driver.find_element(selector_type, selector)
                logger.info(f"Đã tìm thấy nút đăng nhập với selector: {selector_type}={selector}")
                break
            except NoSuchElementException:
                continue
        else:
            raise NoSuchElementException("Không tìm thấy nút đăng nhập")
        
        # Click nút đăng nhập
        submit_button.click()
        logger.info("Đã nhấn nút đăng nhập")
        
        # Đợi trang sau đăng nhập tải xong
        WebDriverWait(driver, DEFAULT_TIMEOUT).until(
            EC.url_changes(url)
        )
        
        # Kiểm tra đăng nhập thành công
        current_url = driver.current_url
        if "login" in current_url.lower() or "signin" in current_url.lower():
            logger.error("Đăng nhập thất bại, vẫn ở trang đăng nhập")
            return False
        
        logger.info(f"Đăng nhập thành công, đã chuyển đến URL: {current_url}")
        
        # Chụp ảnh sau khi đăng nhập
        screenshot_path = f'{SCREENSHOTS_DIR}/after_login_{get_timestamp()}.png'
        take_full_page_screenshot(driver, screenshot_path)
        
        return True
    
    except TimeoutException as e:
        logger.error(f"Timeout khi đăng nhập: {e}")
        return False
    except NoSuchElementException as e:
        logger.error(f"Không tìm thấy phần tử đăng nhập: {e}")
        return False
    except Exception as e:
        logger.error(f"Lỗi khi đăng nhập: {e}")
        traceback.print_exc()
        return False

def access_page(driver, url):
    """
    Truy cập một trang web
    """
    logger.info(f"Đang truy cập trang: {url}")
    try:
        driver.get(url)
        # Đợi trang tải xong
        WebDriverWait(driver, DEFAULT_TIMEOUT).until(
            EC.presence_of_element_located((By.TAG_NAME, 'body'))
        )
        logger.info("Đã tải trang thành công")
        return True
    except TimeoutException as e:
        logger.error(f"Timeout khi truy cập trang: {e}")
        return False
    except Exception as e:
        logger.error(f"Lỗi khi truy cập trang: {e}")
        traceback.print_exc()
        return False
