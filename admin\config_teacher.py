#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration file for teacher extraction in the EN Dance System Admin
"""

# Credentials for logging in to the admin interface
CREDENTIALS = {
    "username": "hirano",  # Replace with your actual admin username
    "password": "hirano"   # Replace with your actual admin password
}

# Base URL for the admin interface
BASE_URL = "https://www.en-system.net/admin/"

# URL for the teacher list page
TEACHER_LIST_URL = BASE_URL + "tool/teacher_main.php"

# Configuration for pagination
PAGINATION = {
    "records_per_page": 50,  # Number of teachers per page
    "js_function": "SetStartNo2",  # JavaScript function for pagination
    "page_info_xpath": "//td[contains(text(), '件中') and contains(text(), '件')]"  # XPath for page info text
}

# Configuration for teacher list table
TEACHER_TABLE = {
    "table_xpath": "//table[contains(@class, 'list_tbl') or contains(@class, 'newsTbl') or contains(@class, 'IchiranTbl2') or contains(@summary, 'お知らせ一覧')]",
    "row_xpath": ".//tr[position() > 1]",  # Skip header row
    "columns": [
        {"name": "id", "index": 0},
        {"name": "public", "index": 1},
        {"name": "type", "index": 2},
        {"name": "category", "index": 3},
        {"name": "name", "index": 4},
        {"name": "studio", "index": 5}
    ]
}

# Configuration for extraction
EXTRACTION = {
    "timeout": 30,  # Timeout in seconds for page loading
    "wait_time": 1,  # Wait time in seconds after page navigation
    "max_retries": 3,  # Maximum number of retries for failed operations
    "save_screenshots": True,  # Whether to save screenshots during extraction
    "save_page_source": True,  # Whether to save page source during extraction
    "debug_mode": True  # Whether to enable debug mode with more logging
}

# Configuration for output
OUTPUT = {
    "base_dir": "teachers",  # Base directory for output
    "teacher_dir_prefix": "teacher_",  # Prefix for teacher directories
    "page_dir_prefix": "page_",  # Prefix for page directories
    "log_file": "extraction.log",  # Log file name
    "stats_file": "extraction_stats.json",  # Stats file name
    "checkpoint_file": "checkpoint.json"  # Checkpoint file name
}
