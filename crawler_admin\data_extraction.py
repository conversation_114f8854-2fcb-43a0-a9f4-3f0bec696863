# data_extraction.py
import re
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import traceback
from config import DEFAULT_TIMEOUT
from logger import logger

def find_table(driver):
    """
    Tìm bảng dữ liệu trong trang
    """
    logger.info("Đang tìm bảng dữ liệu...")
    
    # Danh sách các selector để tìm bảng
    table_selectors = [
        (By.XPATH, "//table[contains(@class, 'IchiranTbl')]"),
        (By.XPATH, "//table[contains(@class, 'data')]"),
        (By.XPATH, "//table[contains(@class, 'list')]"),
        (By.XPATH, "//table[contains(@class, 'tbl')]"),
        (By.XPATH, "//div[contains(@class, 'table')]//table"),
        (By.TAG_NAME, "table")
    ]
    
    for selector_type, selector in table_selectors:
        try:
            tables = driver.find_elements(selector_type, selector)
            if tables:
                # Tìm bảng có nhiều hàng nhất
                max_rows = 0
                largest_table = None
                
                for table in tables:
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    if len(rows) > max_rows:
                        max_rows = len(rows)
                        largest_table = table
                
                if largest_table:
                    logger.info(f"Đã tìm thấy bảng với {max_rows} hàng sử dụng selector: {selector_type}={selector}")
                    return largest_table
        except Exception as e:
            logger.warning(f"Lỗi khi tìm bảng với selector {selector_type}={selector}: {e}")
    
    logger.error("Không tìm thấy bảng dữ liệu nào")
    return None

def extract_table_data(table, expected_columns=None, is_instructor_page=False):
    """
    Trích xuất dữ liệu từ bảng HTML
    """
    if not table:
        logger.error("Không có bảng để trích xuất dữ liệu")
        return [], []
    
    logger.info("Đang trích xuất dữ liệu từ bảng...")
    
    try:
        # Lấy tất cả các hàng
        rows = table.find_elements(By.TAG_NAME, "tr")
        if not rows:
            logger.error("Không tìm thấy hàng nào trong bảng")
            return [], []
        
        logger.info(f"Bảng có tổng cộng {len(rows)} hàng")
        
        # Xác định số hàng header
        header_rows_count = 0
        for row in rows[:3]:  # Kiểm tra tối đa 3 hàng đầu tiên
            if row.find_elements(By.TAG_NAME, "th"):
                header_rows_count += 1
            else:
                break
        
        if header_rows_count == 0:
            # Nếu không có hàng header với thẻ th, lấy hàng đầu tiên làm header
            header_rows_count = 1
            logger.info("Không tìm thấy thẻ th, sử dụng hàng đầu tiên làm header")
        else:
            logger.info(f"Phát hiện {header_rows_count} hàng header")
        
        # Trích xuất header
        headers = []
        for i in range(header_rows_count):
            header_cells = rows[i].find_elements(By.TAG_NAME, "th")
            if not header_cells:
                header_cells = rows[i].find_elements(By.TAG_NAME, "td")
            
            for cell in header_cells:
                header_text = cell.text.strip()
                if not header_text:
                    header_text = f"Column_{len(headers) + 1}"
                headers.append(header_text)
        
        # In ra headers để debug
        logger.info(f"Headers tìm thấy: {headers}")
        
        # Nếu có expected_columns, kiểm tra xem các cột cần thiết có tồn tại không
        if expected_columns:
            found_columns = set(headers)
            expected_set = set(expected_columns)
            missing_columns = expected_set - found_columns
            
            if missing_columns:
                logger.warning(f"Không tìm thấy các cột mong đợi: {missing_columns}")
            else:
                logger.info("Tìm thấy tất cả các cột mong đợi")
        
        # Trích xuất dữ liệu
        data = []
        for row_index, row in enumerate(rows[header_rows_count:], start=header_rows_count + 1):
            cells = row.find_elements(By.TAG_NAME, "td")
            if not cells:
                continue
            
            row_data = {}
            for i, cell in enumerate(cells):
                if i < len(headers):
                    # Xử lý đặc biệt cho trường Week ＆ Time(ホームページ表示用)
                    if is_instructor_page and ("Week ＆ Time" in headers[i] or "Week" in headers[i]):
                        # Tìm input trong td
                        input_elements = cell.find_elements(By.TAG_NAME, "input")
                        if input_elements:
                            # Lấy giá trị từ input
                            input_value = input_elements[0].get_attribute("value")
                            row_data[headers[i]] = input_value if input_value else ""
                            logger.info(f"Đã tìm thấy input trong trường {headers[i]}, giá trị: {input_value}")
                        else:
                            row_data[headers[i]] = cell.text.strip()
                    else:
                        row_data[headers[i]] = cell.text.strip()
            
            if row_data:
                data.append(row_data)
                if row_index % 10 == 0 or row_index == header_rows_count + 1:
                    logger.info(f"Đã xử lý {row_index - header_rows_count} hàng dữ liệu")
        
        logger.info(f"Đã trích xuất {len(data)} hàng dữ liệu")
        return headers, data
    
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất dữ liệu bảng: {e}")
        traceback.print_exc()
        return [], []

def find_instructor_buttons(driver):
    """
    Tìm các nút インストラクター設定へ
    """
    logger.info("Đang tìm các nút インストラクター設定へ...")
    
    buttons = []
    try:
        # Đợi cho các nút xuất hiện
        WebDriverWait(driver, DEFAULT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, "//input[@type='button' and @value='インストラクター設定へ']"))
        )
        
        # Tìm theo value và type
        buttons = driver.find_elements(By.XPATH, "//input[@type='button' and @value='インストラクター設定へ']")
        
        logger.info(f"Đã tìm thấy {len(buttons)} nút インストラクター設定へ")
        
        # Ghi log thông tin về các nút
        for i, button in enumerate(buttons):
            onclick = button.get_attribute("onclick")
            logger.info(f"Nút {i+1}: onclick={onclick}")
        
        return buttons
    except TimeoutException:
        logger.error("Timeout khi tìm các nút インストラクター設定へ")
        return []
    except Exception as e:
        logger.error(f"Lỗi khi tìm nút インストラクター設定へ: {e}")
        traceback.print_exc()
        return []

def extract_room_id_from_onclick(onclick):
    """
    Trích xuất room_id từ chuỗi onclick
    """
    try:
        # Mẫu: document.course_form.room_id.value=10; document.course_form.submit();
        match = re.search(r'room_id\.value=(\d+)', onclick)
        if match:
            return match.group(1)
        return None
    except Exception as e:
        logger.error(f"Lỗi khi trích xuất room_id: {e}")
        return None
