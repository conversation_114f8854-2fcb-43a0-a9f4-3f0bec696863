# file_utils.py
import os
import json
import pandas as pd
from logger import logger
from config import get_timestamp

def save_html(driver, folder, prefix):
    """
    Lưu nội dung HTML của trang
    """
    timestamp = get_timestamp()
    filename = f"{folder}/{prefix}_{timestamp}.html"
    
    try:
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(filename, 'w', encoding='utf-8') as html_file:
            html_file.write(driver.page_source)
        logger.info(f"Đã lưu nội dung HTML vào file {filename}")
        return filename
    except Exception as e:
        logger.error(f"Lỗi khi lưu HTML: {e}")
        return None

def save_data_files(data, folder, prefix):
    """
    Lưu dữ liệu dưới dạng JSON và CSV
    """
    if not data:
        logger.error("Không có dữ liệu để lưu")
        return False
    
    timestamp = get_timestamp()
    json_filename = f"{folder}/{prefix}_{timestamp}.json"
    csv_filename = f"{folder}/{prefix}_{timestamp}.csv"
    
    try:
        # <PERSON><PERSON><PERSON> b<PERSON><PERSON> thư mục tồn tại
        os.makedirs(os.path.dirname(json_filename), exist_ok=True)
        
        # Lưu dữ liệu dưới dạng JSON
        with open(json_filename, 'w', encoding='utf-8') as json_file:
            json.dump(data, json_file, ensure_ascii=False, indent=4)
        logger.info(f"Đã lưu dữ liệu dạng JSON vào file {json_filename}")
        
        # Lưu dữ liệu dưới dạng CSV
        df = pd.DataFrame(data)
        df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
        logger.info(f"Đã lưu dữ liệu dạng CSV vào file {csv_filename}")
        
        return True
    except Exception as e:
        logger.error(f"Lỗi khi lưu dữ liệu: {e}")
        import traceback
        traceback.print_exc()
        return False
