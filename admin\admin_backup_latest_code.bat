@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo EN Dance System Admin Crawler - Backup Latest Code
echo ===================================================
echo.

REM Get current date and time for the backup folder name
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%"
set "MM=%dt:~4,2%"
set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%"
set "Min=%dt:~10,2%"
set "Sec=%dt:~12,2%"

set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

REM Find the latest version number
set "latest_version=0"
if not exist "backup_src" mkdir "backup_src"
for /d %%d in (backup_src\admin_v*) do (
    set "folder_name=%%~nxd"
    set "version_num=!folder_name:admin_v=!"
    if !version_num! GTR !latest_version! (
        set "latest_version=!version_num!"
    )
)

REM Increment the version number
set /a "new_version=latest_version+1"
echo Latest version: v%latest_version%
echo New version: v%new_version%

REM Create the backup folder
set "backup_folder=backup_src\admin_v%new_version%"
mkdir "%backup_folder%"
echo Created backup folder: %backup_folder%

REM Copy all admin crawler files to the backup folder
echo Copying admin crawler files to %backup_folder%...

REM Main crawler scripts
copy "crawler.py" "%backup_folder%\"
copy "capture_page.py" "%backup_folder%\"

REM Configuration files
copy "config.py" "%backup_folder%\"

REM Batch files
copy "run_debug.bat" "%backup_folder%\"
copy "admin_backup_latest_code.bat" "%backup_folder%\"

REM Create version.md file with timestamp and version info
echo # Admin Crawler v%new_version% > "%backup_folder%\version.md"
echo. >> "%backup_folder%\version.md"
echo Backup created on %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec% >> "%backup_folder%\version.md"
echo. >> "%backup_folder%\version.md"
echo ## Files >> "%backup_folder%\version.md"
echo. >> "%backup_folder%\version.md"
echo - crawler.py: Main admin crawler script >> "%backup_folder%\version.md"
echo - capture_page.py: Page capture utility >> "%backup_folder%\version.md"
echo - config.py: Admin credentials and settings >> "%backup_folder%\version.md"
echo - run_debug.bat: Debug execution script >> "%backup_folder%\version.md"
echo - admin_backup_latest_code.bat: Backup script >> "%backup_folder%\version.md"

echo.
echo Backup completed successfully!
echo Files backed up to: %backup_folder%
echo.

endlocal
