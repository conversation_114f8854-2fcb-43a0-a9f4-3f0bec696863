#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script to extract detailed data for all pages of teachers from EN Dance System Admin
This script implements pagination, interruption support, and enhanced statistics
"""

import os
import sys
import time
import json
import signal
import logging
import traceback
import argparse
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    ElementNotInteractableException,
    StaleElementReferenceException
)
from webdriver_manager.chrome import ChromeDriverManager

# Import configuration
from config_teacher import CREDENTIALS, TEACHER_TABLE, PAGINATION
from config_buttons import BUTTON_TYPES

# Import the optimized teacher extractor
from extract_one_teacher_optimized import OptimizedTeacherExtractor, PAGE_STRUCTURE_CACHE

# Update the variables to match the config_teacher format
USERNAME = CREDENTIALS["username"]
PASSWORD = CREDENTIALS["password"]
BASE_URL = "https://www.en-system.net/admin/"
LOGIN_URL = BASE_URL
TEACHER_LIST_URL = BASE_URL + "tool/teacher_main.php"

class AllTeachersExtractor:
    """
    Class to extract detailed data for all pages of teachers
    Implements pagination, interruption support, and enhanced statistics
    """
    
    def __init__(self, output_dir=None, max_pages=None, start_page=1, start_index=0, 
                 time_limit=3, save_screenshots=False, debug_mode=False, resume=False):
        """
        Initialize the extractor
        
        Args:
            output_dir: Directory to save output (default: all_teachers_{timestamp})
            max_pages: Maximum number of pages to extract (default: all pages)
            start_page: Page number to start extraction from (default: 1)
            start_index: Index of the first teacher to extract on the start page (default: 0)
            time_limit: Maximum runtime in hours (default: 3)
            save_screenshots: Whether to save screenshots (default: False)
            debug_mode: Whether to enable debug mode with more logging (default: False)
            resume: Whether to resume from a previous extraction (default: False)
        """
        # Set up output directory
        if not output_dir:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f"all_teachers_{timestamp}"
        
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Configuration
        self.max_pages = max_pages
        self.start_page = start_page
        self.start_index = start_index
        self.time_limit = time_limit
        self.save_screenshots = save_screenshots
        self.debug_mode = debug_mode
        self.button_types = BUTTON_TYPES
        self.resume = resume
        
        # Set up logging
        self.log_file = os.path.join(output_dir, "extraction.log")
        self._setup_logging()
        
        # Initialize stats
        self.stats = {
            "extraction_start": datetime.now(),
            "pages_processed": 0,
            "total_pages": 0,
            "current_page": start_page,
            "teachers_processed": 0,
            "teachers_successful": 0,
            "teachers_failed": 0,
            "teachers": [],
            "errors": [],
            "processed_teacher_ids": [],
            "checkpoints": []
        }
        
        # Runtime tracking
        self.start_time = datetime.now()
        self.end_time = self.start_time + timedelta(hours=time_limit)
        
        # WebDriver
        self.driver = None
        
        # Performance tracking
        self.timings = {
            "login": 0,
            "navigate_to_teacher_list": 0,
            "find_teachers": 0,
            "total_processing": 0,
            "pagination": 0,
            "per_page": {},
            "per_teacher": {}
        }
        
        # Checkpoint file
        self.checkpoint_file = os.path.join(output_dir, "checkpoint.json")
        
        # Interrupt flag
        self.interrupted = False
        
        # Load checkpoint if resuming
        if resume:
            self._load_checkpoint()
        
        self.logger.info("Initialized AllTeachersExtractor")
        self.logger.info(f"Output directory: {self.output_dir}")
        self.logger.info(f"Max pages: {self.max_pages}")
        self.logger.info(f"Start page: {self.start_page}")
        self.logger.info(f"Start index: {self.start_index}")
        self.logger.info(f"Time limit: {self.time_limit} hours")
        self.logger.info(f"Save screenshots: {self.save_screenshots}")
        self.logger.info(f"Debug mode: {self.debug_mode}")
        self.logger.info(f"Resume: {self.resume}")
    
    def _setup_logging(self):
        """Set up logging configuration"""
        self.logger = logging.getLogger("AllTeachersExtractor")
        
        # Set logging level based on debug mode
        log_level = logging.DEBUG if self.debug_mode else logging.INFO
        self.logger.setLevel(log_level)
        
        # Check if the logger already has handlers to avoid duplicates
        if not self.logger.handlers:
            # File handler
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(log_level)
            
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            
            # Formatter
            formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # Add handlers
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
        else:
            # If handlers already exist, just update their log level
            for handler in self.logger.handlers:
                handler.setLevel(log_level)
    
    def _log_error(self, message, error=None):
        """Log an error with optional exception details"""
        if error:
            error_msg = f"{message}: {str(error)}"
            if self.debug_mode:
                stack_trace = traceback.format_exc()
                self.logger.error(f"{error_msg}\n{stack_trace}")
            else:
                self.logger.error(error_msg)
            self.stats["errors"].append(error_msg)
        else:
            self.logger.error(message)
            self.stats["errors"].append(message)
    
    def _initialize_driver(self):
        """Initialize the Selenium WebDriver"""
        try:
            # Set Chrome options
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-infobars")
            
            # Add performance options
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-gpu")
            
            # Always enable images
            chrome_prefs = {
                "profile.default_content_setting_values": {
                    "images": 1  # 1 = allow, 2 = block
                }
            }
            chrome_options.add_experimental_option("prefs", chrome_prefs)
            
            # Initialize driver
            self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
            self.driver.set_page_load_timeout(30)
            
            # Create a WebDriverWait instance
            self.wait = WebDriverWait(self.driver, 10)
            
            return True
        except Exception as e:
            self._log_error("Failed to initialize WebDriver", e)
            return False
    
    def _save_screenshot(self, directory, filename):
        """Save a screenshot to the specified directory if save_screenshots is enabled"""
        if not self.save_screenshots and not self.debug_mode:
            return None
            
        try:
            os.makedirs(directory, exist_ok=True)
            screenshot_path = os.path.join(directory, filename)
            self.driver.save_screenshot(screenshot_path)
            if self.debug_mode:
                self.logger.debug(f"Saved screenshot to {screenshot_path}")
            return screenshot_path
        except Exception as e:
            self._log_error(f"Failed to save screenshot {filename}", e)
            return None
    
    def _save_page_source(self, directory, filename):
        """Save the page source to the specified directory if in debug mode"""
        if not self.debug_mode:
            return None
            
        try:
            os.makedirs(directory, exist_ok=True)
            html_path = os.path.join(directory, filename)
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            self.logger.debug(f"Saved page source to {html_path}")
            return html_path
        except Exception as e:
            self._log_error(f"Failed to save page source {filename}", e)
            return None
    
    def _save_data_as_json(self, directory, filename, data):
        """Save data as JSON to the specified directory"""
        try:
            os.makedirs(directory, exist_ok=True)
            json_path = os.path.join(directory, filename)
            
            # Convert datetime objects to strings
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, datetime):
                        data[key] = value.strftime("%Y-%m-%d %H:%M:%S")
            
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            if self.debug_mode:
                self.logger.debug(f"Saved JSON data to {json_path}")
            return json_path
        except Exception as e:
            self._log_error(f"Failed to save JSON data {filename}", e)
            return None
    
    def _save_checkpoint(self):
        """Save checkpoint to resume later"""
        try:
            checkpoint = {
                "current_page": self.stats["current_page"],
                "current_index": self.stats.get("current_index", 0),
                "teachers_processed": self.stats["teachers_processed"],
                "processed_teacher_ids": self.stats["processed_teacher_ids"],
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # Add to checkpoints history
            self.stats["checkpoints"].append(checkpoint)
            
            # Save checkpoint file
            with open(self.checkpoint_file, "w", encoding="utf-8") as f:
                json.dump(checkpoint, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Saved checkpoint to {self.checkpoint_file}")
            self.logger.info(f"Current page: {checkpoint['current_page']}, Current index: {checkpoint['current_index']}")
            self.logger.info(f"Teachers processed: {checkpoint['teachers_processed']}")
            
            return True
        except Exception as e:
            self._log_error("Failed to save checkpoint", e)
            return False
    
    def _load_checkpoint(self):
        """Load checkpoint to resume extraction"""
        try:
            if not os.path.exists(self.checkpoint_file):
                self.logger.warning(f"Checkpoint file not found: {self.checkpoint_file}")
                return False
            
            with open(self.checkpoint_file, "r", encoding="utf-8") as f:
                checkpoint = json.load(f)
            
            # Update stats from checkpoint
            self.start_page = checkpoint.get("current_page", 1)
            self.start_index = checkpoint.get("current_index", 0)
            self.stats["current_page"] = self.start_page
            self.stats["current_index"] = self.start_index
            self.stats["teachers_processed"] = checkpoint.get("teachers_processed", 0)
            self.stats["processed_teacher_ids"] = checkpoint.get("processed_teacher_ids", [])
            
            self.logger.info(f"Loaded checkpoint from {self.checkpoint_file}")
            self.logger.info(f"Resuming from page {self.start_page}, index {self.start_index}")
            self.logger.info(f"Teachers already processed: {len(self.stats['processed_teacher_ids'])}")
            
            return True
        except Exception as e:
            self._log_error("Failed to load checkpoint", e)
            return False
    
    def _check_time_limit(self):
        """Check if the time limit has been reached"""
        current_time = datetime.now()
        if current_time >= self.end_time:
            self.logger.info(f"Time limit of {self.time_limit} hours reached")
            self.logger.info(f"Started at: {self.start_time}")
            self.logger.info(f"Current time: {current_time}")
            self.logger.info(f"End time: {self.end_time}")
            return True
        return False
    
    def _setup_interrupt_handler(self):
        """Set up handler for keyboard interrupts"""
        def signal_handler(sig, frame):
            self.logger.info("Keyboard interrupt detected - Stopping gracefully...")
            self.interrupted = True
            
            # Save checkpoint immediately
            self._save_checkpoint()
            
            # Save stats
            self.save_stats()
            
            # Close WebDriver
            self.close()
            
            # Exit with a non-zero status code
            self.logger.info("Exiting due to keyboard interrupt. Run with --resume to continue from this point.")
            sys.exit(1)
        
        signal.signal(signal.SIGINT, signal_handler)
        self.logger.info("Interrupt handler set up")
    
    def login(self):
        """Log into the system with enhanced reliability"""
        start_time = time.time()
        try:
            self.logger.info("Starting login process")
            
            # Initialize driver if not already done
            if not self.driver:
                if not self._initialize_driver():
                    return False
            
            # Navigate to login page
            self.driver.get(LOGIN_URL)
            self.logger.info(f"Navigated to login page: {LOGIN_URL}")
            
            # Save screenshot and page source for analysis in debug mode
            self._save_screenshot(self.output_dir, "login_page.png")
            self._save_page_source(self.output_dir, "login_page.html")
            
            # Try automated login with enhanced reliability
            try:
                # Wait for page to fully load
                time.sleep(1)
                
                # Simplified direct approach to login
                self.logger.info("Using direct login approach")
                
                # Take a screenshot of the login page for debugging
                if self.debug_mode:
                    self._save_screenshot(self.output_dir, "login_page_before_input.png")
                    self.logger.debug(f"Page title: {self.driver.title}")
                    self.logger.debug(f"Current URL: {self.driver.current_url}")
                
                # Use JavaScript to fill the login form directly
                login_script = f"""
                // Find the username field
                var usernameField = document.querySelector('input[name="login_id"]');
                if (usernameField) {{
                    usernameField.value = "{USERNAME}";
                    console.log("Set username to {USERNAME}");
                }} else {{
                    console.error("Username field not found");
                }}
                
                // Find the password field
                var passwordField = document.querySelector('input[name="login_pass"]');
                if (passwordField) {{
                    passwordField.value = "{PASSWORD}";
                    console.log("Set password");
                }} else {{
                    console.error("Password field not found");
                }}
                
                return {{
                    username: usernameField ? true : false,
                    password: passwordField ? true : false
                }};
                """
                
                # Execute the script
                form_results = self.driver.execute_script(login_script)
                self.logger.info(f"Form field detection results: {form_results}")
                
                # Take another screenshot after filling the form in debug mode
                if self.debug_mode:
                    self._save_screenshot(self.output_dir, "login_page_after_input.png")
                
                # Find login button with multiple selector options
                login_button = None
                selectors = [
                    (By.CSS_SELECTOR, "input[type='submit']"),
                    (By.XPATH, "//button[contains(text(), 'ログイン')]"),
                    (By.XPATH, "//input[@value='ログイン']"),
                    (By.CLASS_NAME, "btnLogin")
                ]
                
                for selector_type, selector in selectors:
                    try:
                        login_button = self.wait.until(EC.element_to_be_clickable((selector_type, selector)))
                        self.logger.info(f"Found login button using selector: {selector}")
                        break
                    except:
                        continue
                
                if not login_button:
                    raise Exception("Could not find login button with any selector")
                
                # Click login button
                login_button.click()
                self.logger.info("Clicked login button")
                
                # Wait for login to complete with more reliable check
                success_indicators = [
                    "//body[contains(., '本部用ツール')]",
                    "//a[contains(@href, 'logout.php')]",
                    "//div[contains(@class, 'header') and contains(., '本部')]"
                ]
                
                login_success = False
                for indicator in success_indicators:
                    try:
                        self.wait.until(EC.presence_of_element_located((By.XPATH, indicator)))
                        login_success = True
                        self.logger.info(f"Login verified with indicator: {indicator}")
                        break
                    except:
                        continue
                
                if not login_success:
                    raise Exception("Could not verify successful login with any indicator")
                
                self.logger.info("Login completed successfully")
                
                # Save screenshot and page source after login in debug mode
                self._save_screenshot(self.output_dir, "after_login.png")
                self._save_page_source(self.output_dir, "after_login.html")
                
                return True
            
            except Exception as e:
                self.logger.warning(f"Automated login failed: {str(e)}")
                self.logger.info("Falling back to manual login method")
                
                # Manual login approach - wait for redirect or timeout
                max_wait = 60  # seconds
                start_time = time.time()
                
                print("\n*** MANUAL LOGIN REQUIRED ***")
                print("Please log in manually in the browser window.")
                print(f"You have {max_wait} seconds to complete the login.")
                
                while time.time() - start_time < max_wait:
                    if "tool/" in self.driver.current_url:
                        self.logger.info("Manual login detected - user logged in successfully")
                        
                        # Save screenshot and page source after login in debug mode
                        self._save_screenshot(self.output_dir, "after_manual_login.png")
                        self._save_page_source(self.output_dir, "after_manual_login.html")
                        
                        return True
                    time.sleep(1)
                
                self._log_error("Login timeout - please log in manually within the time limit")
                return False
        
        except Exception as e:
            self._log_error("Login process failed", e)
            return False
        finally:
            self.timings["login"] = time.time() - start_time
    
    def navigate_to_teacher_list(self):
        """Navigate to the teacher list page"""
        start_time = time.time()
        try:
            self.logger.info(f"Navigating to teacher list page: {TEACHER_LIST_URL}")
            
            # Navigate to teacher list URL
            self.driver.get(TEACHER_LIST_URL)
            
            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Save screenshot and page source in debug mode
            self._save_screenshot(self.output_dir, "teacher_list_before_search.png")
            self._save_page_source(self.output_dir, "teacher_list_before_search.html")
            
            # Click search button to display teachers
            try:
                search_button = self.wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//input[@type='submit' and @name='search' and @value='検　索']")
                ))
                search_button.click()
                self.logger.info("Clicked search button to display teachers")
                time.sleep(2)
                
                # Save screenshot and page source after search in debug mode
                self._save_screenshot(self.output_dir, "teacher_list_after_search.png")
                self._save_page_source(self.output_dir, "teacher_list_after_search.html")
            except:
                self.logger.info("Could not find search button, checking if teacher table is already visible")
            
            # Wait for teacher table to be visible - try multiple selectors
            table_selectors = [
                "//table[contains(@class, 'list_tbl')]",
                "//table[contains(@class, 'newsTbl')]",
                "//table[contains(@class, 'IchiranTbl2')]",
                "//table[contains(@summary, 'お知らせ一覧')]"
            ]
            
            teacher_table = None
            for selector in table_selectors:
                try:
                    teacher_table = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    self.logger.info(f"Found teacher table using selector: {selector}")
                    break
                except:
                    continue
            
            if not teacher_table:
                self._log_error("Could not find teacher list table")
                return False
            
            # Save screenshot and page source after finding the table in debug mode
            self._save_screenshot(self.output_dir, "teacher_list_table_found.png")
            self._save_page_source(self.output_dir, "teacher_list_table_found.html")
            
            # Extract pagination information if available
            try:
                # Look for pagination info text
                page_info_text = self.driver.find_element(By.XPATH, PAGINATION["page_info_xpath"]).text
                if page_info_text:
                    self.logger.info(f"Found pagination info: {page_info_text}")
                    
                    # Extract current page and total records
                    import re
                    match = re.search(r'(\d+)件中(\d+)～(\d+)件', page_info_text)
                    if match:
                        total_records = int(match.group(1))
                        start_record = int(match.group(2))
                        end_record = int(match.group(3))
                        
                        self.logger.info(f"Pagination details: Total records: {total_records}, Current page: {start_record}-{end_record}")
                        
                        # Store in stats
                        self.stats["total_records"] = total_records
                        self.stats["records_per_page"] = PAGINATION["records_per_page"]
                        self.stats["total_pages"] = (total_records + PAGINATION["records_per_page"] - 1) // PAGINATION["records_per_page"]
            except:
                self.logger.info("Could not find pagination information")
            
            return True
            
        except Exception as e:
            self._log_error("Failed to navigate to teacher list", e)
            return False
        finally:
            self.timings["navigate_to_teacher_list"] = time.time() - start_time
    
    def navigate_to_page_js(self, page_number):
        """Navigate to a specific page using JavaScript SetStartNo2 function"""
        start_time = time.time()
        try:
            self.logger.info(f"Navigating to page {page_number} using JavaScript")
            
            # If we're already on the first page, no need to navigate
            if page_number == 1:
                self.logger.info("Already on page 1, no navigation needed")
                return True
            
            # Calculate the start index for the requested page
            # Page 1 starts at 1, Page 2 starts at 51, Page 3 starts at 101, etc.
            records_per_page = self.stats.get("records_per_page", PAGINATION["records_per_page"])
            start_index = (page_number - 1) * records_per_page + 1
            
            # Execute the JavaScript function SetStartNo2 (as seen in the HTML)
            script = f"{PAGINATION['js_function']}({start_index});"
            self.driver.execute_script(script)
            self.logger.info(f"Executed JavaScript: {script}")
            
            # Wait for the page to load
            time.sleep(2)
            
            # Save screenshot and page source after JavaScript navigation
            self._save_screenshot(self.output_dir, f"js_nav_page_{page_number}.png")
            self._save_page_source(self.output_dir, f"js_nav_page_{page_number}.html")
            
            # Wait for teacher table to be visible - try multiple selectors
            table_selectors = [
                "//table[contains(@class, 'list_tbl')]",
                "//table[contains(@class, 'newsTbl')]",
                "//table[contains(@class, 'IchiranTbl2')]",
                "//table[contains(@summary, 'お知らせ一覧')]"
            ]
            
            teacher_table = None
            for selector in table_selectors:
                try:
                    teacher_table = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    self.logger.info(f"Found teacher table using selector: {selector}")
                    break
                except:
                    continue
            
            if not teacher_table:
                self._log_error(f"Could not find teacher list table on page {page_number}")
                return False
            
            # Verify we're on the correct page by checking the record range text
            try:
                # Look for text like "1330 件中　51 - 100 件"
                record_info_elements = self.driver.find_elements(
                    By.XPATH, 
                    PAGINATION["page_info_xpath"]
                )
                
                if record_info_elements:
                    record_info_text = record_info_elements[0].text
                    self.logger.info(f"Found record info text: {record_info_text}")
                    
                    # Extract range using regex
                    import re
                    range_match = re.search(r'(\d+)\s*-\s*(\d+)\s*件', record_info_text)
                    
                    if range_match:
                        range_start = int(range_match.group(1))
                        range_end = int(range_match.group(2))
                        
                        expected_start = start_index
                        expected_end = min(start_index + records_per_page - 1, self.stats.get("total_records", 1000))
                        
                        self.logger.info(f"Page range: {range_start}-{range_end}, Expected: {expected_start}-{expected_end}")
                        
                        # Check if we're in the right range (allow some flexibility)
                        if abs(range_start - expected_start) > 5 or abs(range_end - expected_end) > 5:
                            self.logger.warning(f"Page range {range_start}-{range_end} doesn't match expected range {expected_start}-{expected_end}")
                            # We'll continue anyway, as we might still be on the correct page
            except Exception as e:
                self.logger.warning(f"Error verifying page range: {str(e)}")
            
            # Update current page in stats
            self.stats["current_page"] = page_number
            return True
            
        except Exception as e:
            self._log_error(f"Failed to navigate to page {page_number} using JavaScript", e)
            return False
        finally:
            self.timings["pagination"] += time.time() - start_time
    
    def navigate_to_page(self, page_number):
        """Navigate to a specific page of the teacher list"""
        start_time = time.time()
        try:
            self.logger.info(f"Navigating to page {page_number}")
            
            # If we're already on the first page, no need to navigate
            if page_number == 1:
                self.logger.info("Already on page 1, no navigation needed")
                return True
            
            # Use JavaScript navigation
            return self.navigate_to_page_js(page_number)
            
        except Exception as e:
            self._log_error(f"Failed to navigate to page {page_number}", e)
            return False
        finally:
            self.timings["pagination"] = time.time() - start_time
    
    def detect_total_pages(self):
        """Detect the total number of pages in the teacher list"""
        try:
            self.logger.info("Detecting total number of pages")
            
            # Look for pagination info text
            page_info_elements = self.driver.find_elements(
                By.XPATH, 
                PAGINATION["page_info_xpath"]
            )
            
            if page_info_elements:
                page_info_text = page_info_elements[0].text
                self.logger.info(f"Found pagination info text: {page_info_text}")
                
                # Extract total records using regex
                import re
                total_records_match = re.search(r'(\d+)\s*件中', page_info_text)
                
                if total_records_match:
                    total_records = int(total_records_match.group(1))
                    self.logger.info(f"Total records: {total_records}")
                    
                    # Store total records in stats
                    self.stats["total_records"] = total_records
                    
                    # Calculate total pages
                    records_per_page = PAGINATION["records_per_page"]
                    total_pages = (total_records + records_per_page - 1) // records_per_page
                    
                    self.logger.info(f"Calculated total pages: {total_pages}")
                    self.stats["total_pages"] = total_pages
                    self.stats["records_per_page"] = records_per_page
                    return total_pages
            
            # If we couldn't parse the record count text, use a default value
            self.logger.warning("Could not determine total pages, using default value of 5")
            self.stats["total_pages"] = 5
            return 5
            
        except Exception as e:
            self._log_error("Failed to detect total pages", e)
            self.stats["total_pages"] = 5
            return 5
    
    def find_teachers_on_page(self):
        """Find teachers on the current page"""
        start_time = time.time()
        try:
            self.logger.info("Finding teachers on the current page")
            
            # Find the teacher table - try multiple selectors
            table_selectors = [
                "//table[contains(@class, 'list_tbl')]",
                "//table[contains(@class, 'newsTbl')]",
                "//table[contains(@class, 'IchiranTbl2')]",
                "//table[contains(@summary, 'お知らせ一覧')]"
            ]
            
            teacher_table = None
            for selector in table_selectors:
                try:
                    teacher_table = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    self.logger.info(f"Found teacher table using selector: {selector}")
                    break
                except:
                    continue
            
            if not teacher_table:
                self._log_error("Could not find teacher list table")
                return []
            
            # Get all rows (skip header row)
            rows = teacher_table.find_elements(By.TAG_NAME, "tr")[1:]
            
            if not rows:
                self._log_error("No teacher rows found in table")
                return []
            
            self.logger.info(f"Found {len(rows)} teachers on the page")
            
            # Extract basic info for each teacher
            teacher_infos = []
            
            for i, row in enumerate(rows):
                try:
                    # Extract teacher information
                    cells = row.find_elements(By.TAG_NAME, "td")
                    
                    if len(cells) < 6:
                        self.logger.warning(f"Not enough cells in teacher row {i} (found {len(cells)}, expected at least 6)")
                        continue
                    
                    # Extract teacher ID and name
                    teacher_id = cells[0].text.strip()
                    teacher_public = cells[1].text.strip()
                    teacher_type = cells[2].text.strip()
                    teacher_category = cells[3].text.strip()
                    teacher_name = cells[4].text.strip()
                    teacher_studio = cells[5].text.strip()
                    
                    # Create teacher info dictionary
                    teacher_info = {
                        "id": teacher_id,
                        "public": teacher_public,
                        "type": teacher_type,
                        "category": teacher_category,
                        "name": teacher_name,
                        "studio": teacher_studio,
                        "row_index": i,
                        "page": self.stats["current_page"],
                        "row_html": row.get_attribute("outerHTML"),
                        "cells": [cell.text.strip() for cell in cells]
                    }
                    
                    # Check if this teacher has already been processed
                    if teacher_id in self.stats["processed_teacher_ids"]:
                        self.logger.info(f"Teacher {teacher_id} has already been processed, skipping")
                        continue
                    
                    teacher_infos.append(teacher_info)
                    self.logger.info(f"Found teacher {i+1}/{len(rows)}: {teacher_id} - {teacher_name}")
                    
                except Exception as e:
                    self.logger.warning(f"Error extracting info for teacher at index {i}: {str(e)}")
            
            # Save all teacher infos for this page
            page_dir = os.path.join(self.output_dir, f"page_{self.stats['current_page']}")
            os.makedirs(page_dir, exist_ok=True)
            self._save_data_as_json(page_dir, "teachers_info.json", teacher_infos)
            
            return teacher_infos
            
        except Exception as e:
            self._log_error("Failed to find teachers", e)
            return []
        finally:
            self.timings["find_teachers"] += time.time() - start_time
    
    def process_teacher(self, teacher_info):
        """Process a single teacher"""
        teacher_start_time = time.time()
        teacher_id = teacher_info["id"]
        teacher_name = teacher_info["name"]
        
        self.logger.info(f"Processing teacher: {teacher_id} - {teacher_name}")
        
        # Create directory for this teacher
        teacher_dir = os.path.join(self.output_dir, f"teacher_{teacher_id}")
        os.makedirs(teacher_dir, exist_ok=True)
        
        # Save teacher info
        self._save_data_as_json(teacher_dir, "teacher_info.json", teacher_info)
        
        try:
            # Create an optimized teacher extractor for this teacher
            extractor = OptimizedTeacherExtractor(
                output_dir=teacher_dir,  # Use the teacher directory directly, not a subdirectory
                teacher_index=teacher_info["row_index"],
                save_screenshots=self.save_screenshots,
                debug_mode=self.debug_mode
            )
            
            # Share the WebDriver instance and wait object
            extractor.driver = self.driver
            extractor.wait = self.wait
            
            # Set the teacher ID and name in the extractor's stats
            extractor.stats["teacher_id"] = teacher_id
            extractor.stats["teacher_name"] = teacher_name
            
            # Save the updated stats
            extractor._save_data_as_json(teacher_dir, "extraction_stats.json", extractor.stats)
            
            # Process the teacher
            success = extractor.process_teacher_buttons()
            
            # Update our stats
            self.stats["teachers_processed"] += 1
            self.stats["processed_teacher_ids"].append(teacher_id)
            
            if success:
                self.stats["teachers_successful"] += 1
                self.stats["teachers"].append({
                    "id": teacher_id,
                    "name": teacher_name,
                    "page": teacher_info["page"],
                    "success": True,
                    "directory": teacher_dir
                })
            else:
                self.stats["teachers_failed"] += 1
                self.stats["teachers"].append({
                    "id": teacher_id,
                    "name": teacher_name,
                    "page": teacher_info["page"],
                    "success": False,
                    "directory": teacher_dir
                })
            
            # Record timing for this teacher
            teacher_time = time.time() - teacher_start_time
            self.timings["per_teacher"][teacher_id] = teacher_time
            self.logger.info(f"Processed teacher {teacher_id} in {teacher_time:.2f} seconds")
            
            # Save checkpoint after each teacher
            self.stats["current_index"] = teacher_info["row_index"] + 1
            self._save_checkpoint()
            
            return success
            
        except Exception as e:
            self._log_error(f"Failed to process teacher {teacher_id}", e)
            self.stats["teachers_failed"] += 1
            self.stats["teachers"].append({
                "id": teacher_id,
                "name": teacher_name,
                "page": teacher_info["page"],
                "success": False,
                "directory": teacher_dir,
                "error": str(e)
            })
            
            # Save checkpoint even if there was an error
            self.stats["current_index"] = teacher_info["row_index"] + 1
            self._save_checkpoint()
            
            return False
    
    def process_page(self, page_number):
        """Process all teachers on a specific page"""
        page_start_time = time.time()
        try:
            self.logger.info(f"Processing page {page_number}")
            
            # Navigate to the page
            if not self.navigate_to_page(page_number):
                self._log_error(f"Failed to navigate to page {page_number}")
                return False
            
            # Find teachers on the page
            teacher_infos = self.find_teachers_on_page()
            if not teacher_infos:
                self.logger.warning(f"No teachers found on page {page_number}")
                return True  # Return True to continue to the next page
            
            # Determine the starting index for this page
            start_index = self.start_index if page_number == self.start_page else 0
            
            # Process each teacher
            for i, teacher_info in enumerate(teacher_infos):
                # Skip teachers before the start index
                if i < start_index:
                    self.logger.info(f"Skipping teacher at index {i} (before start index {start_index})")
                    continue
                
                # Check if we should stop due to time limit or interruption
                if self._check_time_limit() or self.interrupted:
                    self.logger.info("Stopping teacher processing due to time limit or interruption")
                    return False
                
                # Process the teacher
                self.process_teacher(teacher_info)
                
                # Navigate back to teacher list for the next teacher
                self.navigate_to_teacher_list()
                
                # Navigate back to the current page
                if not self.navigate_to_page(page_number):
                    self._log_error(f"Failed to navigate back to page {page_number}")
                    return False
            
            # Update stats
            self.stats["pages_processed"] += 1
            
            # Record timing for this page
            page_time = time.time() - page_start_time
            self.timings["per_page"][f"page_{page_number}"] = page_time
            self.logger.info(f"Processed page {page_number} in {page_time:.2f} seconds")
            
            return True
            
        except Exception as e:
            self._log_error(f"Failed to process page {page_number}", e)
            return False
        finally:
            # Reset start index after processing the start page
            if page_number == self.start_page:
                self.start_index = 0
    
    def save_stats(self):
        """Save extraction statistics to a file"""
        try:
            # Update end time
            self.stats["extraction_end"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Calculate duration
            if isinstance(self.stats["extraction_start"], datetime):
                start_time = self.stats["extraction_start"]
                # Convert start time to string for JSON
                self.stats["extraction_start"] = start_time.strftime("%Y-%m-%d %H:%M:%S")
            else:
                # If it's already a string, try to parse it
                try:
                    start_time = datetime.strptime(str(self.stats["extraction_start"]), "%Y-%m-%d %H:%M:%S.%f")
                except ValueError:
                    try:
                        start_time = datetime.strptime(str(self.stats["extraction_start"]), "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        # If all parsing fails, use current time as fallback
                        start_time = datetime.now()
                        self.stats["extraction_start"] = start_time.strftime("%Y-%m-%d %H:%M:%S")
                        self.logger.warning("Could not parse extraction start time, using current time as fallback")
            
            end_time = datetime.strptime(self.stats["extraction_end"], "%Y-%m-%d %H:%M:%S")
            
            # Calculate duration
            if isinstance(start_time, datetime):
                duration = end_time - start_time
                self.stats["duration_seconds"] = duration.total_seconds()
            else:
                # If we couldn't parse the start time, just use a placeholder duration
                self.stats["duration_seconds"] = 0
                self.logger.warning("Could not calculate duration, using 0 as fallback")
            
            # Add performance timings
            self.stats["timings"] = self.timings
            
            # Save stats as JSON
            stats_file = os.path.join(self.output_dir, "extraction_stats.json")
            with open(stats_file, "w", encoding="utf-8") as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Saved extraction statistics to {stats_file}")
            
            # Log summary
            self.logger.info("----- Extraction Summary -----")
            self.logger.info(f"Pages processed: {self.stats['pages_processed']}/{self.stats['total_pages']}")
            self.logger.info(f"Teachers processed: {self.stats['teachers_processed']}")
            self.logger.info(f"Teachers successful: {self.stats['teachers_successful']}")
            self.logger.info(f"Teachers failed: {self.stats['teachers_failed']}")
            self.logger.info(f"Duration: {self.stats.get('duration_seconds', 0):.2f} seconds")
            self.logger.info(f"Errors: {len(self.stats['errors'])}")
            
            # Log performance timings
            self.logger.info("----- Performance Timings -----")
            self.logger.info(f"Login: {self.timings['login']:.2f} seconds")
            self.logger.info(f"Navigate to teacher list: {self.timings['navigate_to_teacher_list']:.2f} seconds")
            self.logger.info(f"Find teachers: {self.timings['find_teachers']:.2f} seconds")
            self.logger.info(f"Pagination: {self.timings['pagination']:.2f} seconds")
            self.logger.info(f"Total processing: {self.timings['total_processing']:.2f} seconds")
            
            # Calculate average time per teacher
            if self.stats["teachers_processed"] > 0:
                avg_time = sum(self.timings["per_teacher"].values()) / self.stats["teachers_processed"]
                self.logger.info(f"Average time per teacher: {avg_time:.2f} seconds")
            
            # Calculate average time per page
            if self.stats["pages_processed"] > 0:
                avg_time = sum(self.timings["per_page"].values()) / self.stats["pages_processed"]
                self.logger.info(f"Average time per page: {avg_time:.2f} seconds")
            
            self.logger.info("----------------------------")
            
            return stats_file
        except Exception as e:
            self._log_error(f"Failed to save statistics: {str(e)}")
            return None
    
    def close(self):
        """Close the WebDriver"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver closed")
        except Exception as e:
            self._log_error("Failed to close WebDriver", e)
    
    def run(self):
        """Run the extraction process for all pages"""
        start_time = time.time()
        try:
            self.logger.info("Starting extraction process")
            
            # Set up interrupt handler
            self._setup_interrupt_handler()
            
            # Initialize WebDriver
            if not self.driver:
                if not self._initialize_driver():
                    self._log_error("Failed to initialize WebDriver, cannot continue")
                    return False
            
            # Login
            if not self.login():
                self._log_error("Login failed, cannot continue")
                return False
            
            # Navigate to teacher list
            if not self.navigate_to_teacher_list():
                self._log_error("Failed to navigate to teacher list, cannot continue")
                return False
            
            # Detect total pages
            total_pages = self.detect_total_pages()
            
            # Check if the start page is valid
            if self.start_page > total_pages:
                self._log_error(f"Start page {self.start_page} is greater than total pages {total_pages}")
                self.logger.info(f"Adjusting start page to 1")
                self.start_page = 1
            
            # Determine the range of pages to process
            end_page = total_pages
            if self.max_pages:
                end_page = min(self.start_page + self.max_pages - 1, total_pages)
            
            self.logger.info(f"Will process pages {self.start_page} to {end_page} (total: {total_pages})")
            
            # Process each page
            for page_number in range(self.start_page, end_page + 1):
                # Check if we should stop due to time limit or interruption
                if self._check_time_limit() or self.interrupted:
                    self.logger.info("Stopping page processing due to time limit or interruption")
                    break
                
                # Process the page
                if not self.process_page(page_number):
                    # If page processing failed, try to continue with the next page
                    self.logger.warning(f"Failed to process page {page_number}, trying to continue with next page")
                    
                    # Navigate back to teacher list
                    self.navigate_to_teacher_list()
                    
                    # If we were interrupted or hit the time limit, break the loop
                    if self._check_time_limit() or self.interrupted:
                        break
            
            # Save stats
            self.save_stats()
            
            # Check if we completed all pages
            if self.stats["pages_processed"] >= total_pages:
                self.logger.info("Extraction process completed successfully (all pages processed)")
                return True
            else:
                self.logger.info(f"Extraction process completed partially ({self.stats['pages_processed']}/{total_pages} pages processed)")
                
                # Provide information about resuming
                self.logger.info("To resume extraction, run with --resume option")
                return True
            
        except Exception as e:
            self._log_error("Extraction process failed", e)
            return False
        finally:
            # Save final stats
            self.save_stats()
            
            # Record total processing time
            self.timings["total_processing"] = time.time() - start_time
            
            # Close WebDriver
            self.close()


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Extract detailed data for all pages of teachers with pagination, interruption support, and enhanced statistics")
    parser.add_argument("-o", "--output", help="Output directory (default: all_teachers_{timestamp})")
    parser.add_argument("-p", "--max-pages", type=int, help="Maximum number of pages to extract (default: all pages)")
    parser.add_argument("-s", "--start-page", type=int, default=1, help="Page number to start extraction from (default: 1)")
    parser.add_argument("-i", "--start-index", type=int, default=0, help="Index of the first teacher to extract on the start page (default: 0)")
    parser.add_argument("-t", "--time-limit", type=float, default=3, help="Maximum runtime in hours (default: 3)")
    parser.add_argument("--screenshots", action="store_true", help="Save screenshots during extraction")
    parser.add_argument("-d", "--debug", action="store_true", help="Enable debug mode with more logging")
    parser.add_argument("-r", "--resume", action="store_true", help="Resume from a previous extraction")
    
    args = parser.parse_args()
    
    # Create extractor
    extractor = AllTeachersExtractor(
        output_dir=args.output,
        max_pages=args.max_pages,
        start_page=args.start_page,
        start_index=args.start_index,
        time_limit=args.time_limit,
        save_screenshots=args.screenshots,
        debug_mode=args.debug,
        resume=args.resume
    )
    
    # Run extraction
    extractor.run()


if __name__ == "__main__":
    main()
