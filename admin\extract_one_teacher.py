#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple script to extract data for a single teacher from EN Dance System Admin
This script focuses on understanding the data structure before extracting all teachers
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager

# Import configuration
from config_teacher import CREDENTIALS
from config_buttons import BUTTON_TYPES, BUTTON_CONFIG, VERIFIED_BUTTON_CONFIG

# Update the variables to match the config_teacher format
USERNAME = CREDENTIALS["username"]
PASSWORD = CREDENTIALS["password"]
BASE_URL = "https://www.en-system.net/admin/"
LOGIN_URL = BASE_URL
TEACHER_LIST_URL = BASE_URL + "tool/teacher_main.php"

def setup_logging(log_file, debug_mode=True):
    """Set up logging configuration"""
    logger = logging.getLogger("TeacherExtractor")
    
    # Set logging level based on debug mode
    log_level = logging.DEBUG if debug_mode else logging.INFO
    logger.setLevel(log_level)
    
    # File handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(log_level)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    
    # Formatter
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def initialize_driver():
    """Initialize the Selenium WebDriver"""
    # Set Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    
    # Add performance options
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-gpu")
    
    # Always enable images
    chrome_prefs = {
        "profile.default_content_setting_values": {
            "images": 1  # 1 = allow, 2 = block
        }
    }
    chrome_options.add_experimental_option("prefs", chrome_prefs)
    
    # Initialize driver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    driver.set_page_load_timeout(30)
    
    return driver

def login(driver, logger):
    """Log into the system"""
    logger.info("Starting login process")
    
    # Navigate to login page
    driver.get(LOGIN_URL)
    logger.info(f"Navigated to login page: {LOGIN_URL}")
    
    # Wait for page to fully load
    time.sleep(1)
    
    # Fill login form
    login_script = f"""
    // Find the username field
    var usernameField = document.querySelector('input[name="login_id"]');
    if (usernameField) {{
        usernameField.value = "{USERNAME}";
        console.log("Set username to {USERNAME}");
    }} else {{
        console.error("Username field not found");
    }}
    
    // Find the password field
    var passwordField = document.querySelector('input[name="login_pass"]');
    if (passwordField) {{
        passwordField.value = "{PASSWORD}";
        console.log("Set password");
    }} else {{
        console.error("Password field not found");
    }}
    
    return {{
        username: usernameField ? true : false,
        password: passwordField ? true : false
    }};
    """
    
    # Execute the script
    form_results = driver.execute_script(login_script)
    logger.info(f"Form field detection results: {form_results}")
    
    # Find and click login button
    wait = WebDriverWait(driver, 10)
    selectors = [
        (By.CSS_SELECTOR, "input[type='submit']"),
        (By.XPATH, "//button[contains(text(), 'ログイン')]"),
        (By.XPATH, "//input[@value='ログイン']"),
        (By.CLASS_NAME, "btnLogin")
    ]
    
    for selector_type, selector in selectors:
        try:
            login_button = wait.until(EC.element_to_be_clickable((selector_type, selector)))
            logger.info(f"Found login button using selector: {selector}")
            login_button.click()
            logger.info("Clicked login button")
            break
        except:
            continue
    
    # Wait for login to complete
    success_indicators = [
        "//body[contains(., '本部用ツール')]",
        "//a[contains(@href, 'logout.php')]",
        "//div[contains(@class, 'header') and contains(., '本部')]"
    ]
    
    login_success = False
    for indicator in success_indicators:
        try:
            wait.until(EC.presence_of_element_located((By.XPATH, indicator)))
            login_success = True
            logger.info(f"Login verified with indicator: {indicator}")
            break
        except:
            continue
    
    if login_success:
        logger.info("Login completed successfully")
        return True
    else:
        logger.error("Login failed")
        return False

def navigate_to_teacher_list(driver, logger):
    """Navigate to the teacher list page"""
    logger.info(f"Navigating to teacher list page: {TEACHER_LIST_URL}")
    
    # Navigate to teacher list URL
    driver.get(TEACHER_LIST_URL)
    
    # Wait for page to load
    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
    
    # Click search button to display teachers
    try:
        search_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable(
            (By.XPATH, "//input[@type='submit' and @name='search' and @value='検　索']")
        ))
        search_button.click()
        logger.info("Clicked search button to display teachers")
        time.sleep(1)
    except:
        logger.info("Could not find search button, checking if teacher table is already visible")
    
    # Wait for teacher table to be visible
    try:
        WebDriverWait(driver, 10).until(EC.presence_of_element_located(
            (By.XPATH, "//table[contains(@class, 'list_tbl') or contains(@class, 'newsTbl')]")
        ))
        logger.info("Teacher table is visible")
        return True
    except TimeoutException:
        logger.error("Could not find teacher list table")
        return False

def find_teacher(driver, logger, teacher_index, output_dir):
    """Find a specific teacher on the current page"""
    logger.info(f"Finding teacher at index {teacher_index}")
    
    # Find the teacher table - try multiple selectors
    table_selectors = [
        "//table[contains(@class, 'list_tbl')]",
        "//table[contains(@class, 'newsTbl')]",
        "//table[contains(@class, 'IchiranTbl2')]",
        "//table[contains(@summary, 'お知らせ一覧')]"
    ]
    
    table = None
    for selector in table_selectors:
        try:
            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, selector)))
            logger.info(f"Found teacher table using selector: {selector}")
            break
        except:
            continue
    
    if not table:
        logger.error("Could not find teacher list table")
        return None
    
    # Get all rows (skip header row)
    rows = table.find_elements(By.TAG_NAME, "tr")[1:]
    
    if not rows:
        logger.error("No teacher rows found in table")
        return None
    
    if teacher_index >= len(rows):
        logger.error(f"Teacher index {teacher_index} is out of range (only {len(rows)} teachers found)")
        return None
    
    # Get the target row
    target_row = rows[teacher_index]
    
    # Extract teacher information
    cells = target_row.find_elements(By.TAG_NAME, "td")
    
    if len(cells) < 6:
        logger.error(f"Not enough cells in teacher row (found {len(cells)}, expected at least 6)")
        return None
    
    # Extract teacher ID and name based on the column structure from the image
    # ID, 公開, 種別, カテゴリー, 名前, 所属教室
    teacher_id = cells[0].text.strip()
    teacher_public = cells[1].text.strip()
    teacher_type = cells[2].text.strip()
    teacher_category = cells[3].text.strip()
    teacher_name = cells[4].text.strip()
    teacher_studio = cells[5].text.strip()
    
    # Create teacher info dictionary
    teacher_info = {
        "id": teacher_id,
        "public": teacher_public,
        "type": teacher_type,
        "category": teacher_category,
        "name": teacher_name,
        "studio": teacher_studio,
        "row_index": teacher_index,
        "row_html": target_row.get_attribute("outerHTML"),
        "cells": [cell.text.strip() for cell in cells]
    }
    
    # Find the buttons for this teacher
    buttons = {}
    for button_type in BUTTON_TYPES:
        try:
            # First try to find the button using the verified selector
            if button_type in VERIFIED_BUTTON_CONFIG:
                selector = VERIFIED_BUTTON_CONFIG[button_type]["selector"]
                try:
                    button = target_row.find_element(By.XPATH, f".{selector}")
                    buttons[button_type] = {
                        "element": button,
                        "href": button.get_attribute("href"),
                        "onclick": button.get_attribute("onclick")
                    }
                    logger.info(f"Found {button_type} button for teacher {teacher_id} using verified selector")
                    continue
                except:
                    logger.debug(f"Could not find {button_type} button using verified selector, trying alternatives")
            
            # Try multiple approaches to find the button
            # 1. Try to find by input value
            try:
                button = target_row.find_element(By.XPATH, f".//input[@value='{button_type}']")
                buttons[button_type] = {
                    "element": button,
                    "href": button.get_attribute("href"),
                    "onclick": button.get_attribute("onclick")
                }
                logger.info(f"Found {button_type} button for teacher {teacher_id} using input value")
                continue
            except:
                pass
            
            # 2. Try to find by link text
            try:
                button = target_row.find_element(By.XPATH, f".//a[contains(text(), '{button_type}')]")
                buttons[button_type] = {
                    "element": button,
                    "href": button.get_attribute("href"),
                    "onclick": button.get_attribute("onclick")
                }
                logger.info(f"Found {button_type} button for teacher {teacher_id} using link text")
                continue
            except:
                pass
            
            # 3. Try to find by any element with the button text
            try:
                button = target_row.find_element(By.XPATH, f".//*[contains(text(), '{button_type}')]")
                buttons[button_type] = {
                    "element": button,
                    "href": button.get_attribute("href"),
                    "onclick": button.get_attribute("onclick")
                }
                logger.info(f"Found {button_type} button for teacher {teacher_id} using any element")
                continue
            except:
                pass
            
            logger.warning(f"Could not find {button_type} button for teacher {teacher_id} using any method")
            buttons[button_type] = None
            
        except Exception as e:
            logger.warning(f"Error finding {button_type} button for teacher {teacher_id}: {str(e)}")
            buttons[button_type] = None
    
    # Add buttons to teacher info
    teacher_info["buttons"] = {
        button_type: {
            "href": buttons[button_type]["href"] if buttons[button_type] else None,
            "onclick": buttons[button_type]["onclick"] if buttons[button_type] else None
        } for button_type in BUTTON_TYPES
    }
    
    # Save teacher info
    os.makedirs(output_dir, exist_ok=True)
    with open(os.path.join(output_dir, "teacher_info.json"), "w", encoding="utf-8") as f:
        json.dump(teacher_info, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Found teacher: {teacher_id} - {teacher_name}")
    
    return {
        "info": teacher_info,
        "buttons": buttons
    }

def extract_detail_page_data(driver, logger, button_type, output_dir, teacher_id):
    """Extract data from a detail page"""
    logger.info(f"Extracting data from {button_type} detail page")
    
    # Create directory for this button type
    button_dir = os.path.join(output_dir, button_type)
    os.makedirs(button_dir, exist_ok=True)
    
    # Save screenshot
    screenshot_path = os.path.join(button_dir, f"{button_type}_page.png")
    driver.save_screenshot(screenshot_path)
    logger.info(f"Saved screenshot to {screenshot_path}")
    
    # Save page source
    html_path = os.path.join(button_dir, f"{button_type}_page.html")
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(driver.page_source)
    logger.info(f"Saved page source to {html_path}")
    
    # Extract form fields
    form_fields = extract_form_fields(driver, logger)
    
    # Extract tables
    tables_data = extract_tables(driver, logger)
    
    # Combine data
    page_data = {
        "form_fields": form_fields,
        "tables": tables_data["tables"],
        "table_metadata": tables_data["metadata"],
        "extraction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "url": driver.current_url,
        "title": driver.title
    }
    
    # Save data
    json_path = os.path.join(button_dir, f"{button_type}_data.json")
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(page_data, f, ensure_ascii=False, indent=2)
    logger.info(f"Saved data to {json_path}")
    
    return page_data

def extract_form_fields(driver, logger):
    """Extract form fields from the current page"""
    form_fields = {}
    
    try:
        # Define selectors for form fields
        selectors = [
            "input[type='text']", 
            "input[type='hidden']",
            "input[type='radio']:checked", 
            "input[type='checkbox']:checked",
            "select", 
            "textarea"
        ]
        
        # Extract form fields using the selectors
        for selector in selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            logger.debug(f"Found {len(elements)} elements with selector: {selector}")
            
            for element in elements:
                try:
                    # Get element ID, name, or other identifier
                    elem_id = element.get_attribute("id") or element.get_attribute("name")
                    
                    if not elem_id:
                        # Skip elements without any identifier
                        continue
                    
                    # Get element value
                    if element.tag_name.lower() == "select":
                        # For select elements, get the selected option text
                        try:
                            selected_option = element.find_element(By.CSS_SELECTOR, "option:checked")
                            value = selected_option.text
                        except:
                            value = element.get_attribute("value")
                    else:
                        # For other input elements, get the value
                        value = element.get_attribute("value")
                    
                    # Store in form fields
                    form_fields[elem_id] = value
                    
                    # Log the field for analysis
                    logger.debug(f"Found form field: {elem_id} = {value}")
                except Exception as e:
                    logger.warning(f"Error extracting field: {str(e)}")
                    continue
        
        logger.info(f"Extracted {len(form_fields)} form fields in total")
        
    except Exception as e:
        logger.error(f"Failed to extract form fields: {str(e)}")
    
    return form_fields

def extract_tables(driver, logger):
    """Extract tables from the current page"""
    tables = []
    table_metadata = []
    
    try:
        # Look for all tables
        table_elements = driver.find_elements(By.TAG_NAME, "table")
        logger.debug(f"Found {len(table_elements)} table elements")
        
        for i, table_element in enumerate(table_elements):
            try:
                # Get table attributes for metadata
                table_id = table_element.get_attribute("id") or f"table_{i+1}"
                table_class = table_element.get_attribute("class") or ""
                
                # Extract table data
                table_data = []
                rows = table_element.find_elements(By.TAG_NAME, "tr")
                
                for row in rows:
                    # Get cells (both th and td)
                    cells = row.find_elements(By.TAG_NAME, "th") + row.find_elements(By.TAG_NAME, "td")
                    row_data = [cell.text.strip() for cell in cells]
                    
                    if any(cell for cell in row_data):  # Skip empty rows
                        table_data.append(row_data)
                
                if table_data:  # Skip empty tables
                    tables.append(table_data)
                    
                    # Store metadata about this table
                    metadata = {
                        "id": table_id,
                        "class": table_class,
                        "rows": len(table_data),
                        "columns": len(table_data[0]) if table_data else 0,
                        "has_header": bool(table_element.find_elements(By.TAG_NAME, "th")),
                        "first_row": table_data[0] if table_data else []
                    }
                    table_metadata.append(metadata)
                    
                    logger.debug(f"Extracted table {table_id} with {len(table_data)} rows")
            except Exception as e:
                logger.warning(f"Error extracting table {i}: {str(e)}")
                continue
    
    except Exception as e:
        logger.error(f"Failed to extract tables: {str(e)}")
    
    return {"tables": tables, "metadata": table_metadata}

def process_teacher(driver, logger, teacher_data, output_dir):
    """Process a teacher by extracting data from all detail pages"""
    logger.info(f"Processing teacher: {teacher_data['info']['id']} - {teacher_data['info']['name']}")
    
    results = {}
    
    # Process each button type
    for button_type in BUTTON_TYPES:
        logger.info(f"Processing {button_type} button")
        
        # Get the button element
        button = teacher_data['buttons'].get(button_type)
        if not button or not button['element']:
            logger.warning(f"No {button_type} button found for this teacher")
            continue
        
        # Click the button
        try:
            # Save screenshot before clicking
            screenshot_path = os.path.join(output_dir, f"before_click_{button_type}.png")
            driver.save_screenshot(screenshot_path)
            logger.info(f"Saved screenshot before clicking {button_type} button")
            
            # Get the onclick attribute to extract the JavaScript function
            onclick = button['element'].get_attribute("onclick")
            logger.info(f"Button onclick attribute: {onclick}")
            
            # Click the button
            button['element'].click()
            logger.info(f"Clicked {button_type} button")
            time.sleep(2)  # Wait for page to load
            
            # Save screenshot after clicking
            screenshot_path = os.path.join(output_dir, f"after_click_{button_type}.png")
            driver.save_screenshot(screenshot_path)
            logger.info(f"Saved screenshot after clicking {button_type} button")
            
            # Extract data from the detail page
            page_data = extract_detail_page_data(
                driver, 
                logger, 
                button_type, 
                output_dir, 
                teacher_data['info']['id']
            )
            
            results[button_type] = page_data
            
            # Navigate back to the teacher list
            if button_type in VERIFIED_BUTTON_CONFIG and VERIFIED_BUTTON_CONFIG[button_type]["back_selector"]:
                # Try to use the back button if configured
                try:
                    back_selector = VERIFIED_BUTTON_CONFIG[button_type]["back_selector"]
                    back_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, back_selector))
                    )
                    back_button.click()
                    logger.info(f"Clicked back button for {button_type}")
                    time.sleep(1)
                except Exception as e:
                    logger.warning(f"Could not use back button for {button_type}: {str(e)}")
                    navigate_to_teacher_list(driver, logger)
            else:
                # Navigate back to the teacher list
                navigate_to_teacher_list(driver, logger)
            
        except Exception as e:
            logger.error(f"Error processing {button_type} button: {str(e)}")
            # Try to navigate back to the teacher list
            navigate_to_teacher_list(driver, logger)
    
    return results

def main():
    """Main function"""
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract data for a single teacher from EN Dance System Admin")
    parser.add_argument("-i", "--index", type=int, default=0, help="Index of the teacher to extract (0 = first teacher)")
    parser.add_argument("-o", "--output", type=str, help="Output directory (default: single_teacher_{timestamp})")
    parser.add_argument("-d", "--debug", action="store_true", help="Enable debug mode with more logging")
    parser.add_argument("-s", "--screenshots", action="store_true", help="Save screenshots during extraction")
    
    args = parser.parse_args()
    
    # Set up output directory
    if args.output:
        output_dir = args.output
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"single_teacher_{timestamp}"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Set up logging
    log_file = os.path.join(output_dir, "extraction.log")
    logger = setup_logging(log_file, debug_mode=args.debug)
    
    logger.info("Starting teacher extraction")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Teacher index: {args.index}")
    logger.info(f"Debug mode: {args.debug}")
    logger.info(f"Save screenshots: {args.screenshots}")
    
    # Initialize WebDriver
    driver = None
    try:
        driver = initialize_driver()
        
        # Login
        if not login(driver, logger):
            logger.error("Login failed, cannot continue")
            return
        
        # Navigate to teacher list
        if not navigate_to_teacher_list(driver, logger):
            logger.error("Failed to navigate to teacher list, cannot continue")
            return
        
        # Find teacher at the specified index
        teacher_data = find_teacher(driver, logger, args.index, output_dir)
        if not teacher_data:
            logger.error("Failed to find teacher, cannot continue")
            return
        
        # Process teacher
        results = process_teacher(driver, logger, teacher_data, output_dir)
        
        # Save overall results
        overall_results = {
            "teacher_id": teacher_data['info']['id'],
            "teacher_name": teacher_data['info']['name'],
            "extraction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "buttons_processed": list(results.keys()),
            "success": True
        }
        
        with open(os.path.join(output_dir, "extraction_results.json"), "w", encoding="utf-8") as f:
            json.dump(overall_results, f, ensure_ascii=False, indent=2)
        
        logger.info("Extraction completed successfully")
        
    except Exception as e:
        logger.error(f"Extraction process failed: {str(e)}")
        
        # Save error information
        error_info = {
            "error": str(e),
            "extraction_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "success": False
        }
        
        with open(os.path.join(output_dir, "extraction_error.json"), "w", encoding="utf-8") as f:
            json.dump(error_info, f, ensure_ascii=False, indent=2)
            
    finally:
        # Close WebDriver
        if driver:
            driver.quit()
            logger.info("WebDriver closed")

if __name__ == "__main__":
    main()
