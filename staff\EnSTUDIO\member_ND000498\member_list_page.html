<html xmlns="http://www.w3.org/1999/xhtml" lang="ja" xml:lang="ja"><head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="Content-Style-Type" content="text/css">
    <meta http-equiv="Content-Script-Type" content="text/javascript">
    <title>EN-DANCE SYSTEM</title>
    <link href="css/import.css" rel="stylesheet" type="text/css" media="all">
    <link href="css/jquery-ui.css" rel="stylesheet" type="text/css" media="all">
    <script type="text/javascript" charset="UTF-8" src="https://www.en-system.net/staff/tool/js/input_check.js"></script>
    <script type="text/javascript" src="js/jquery-1.8.3.js"></script>
    <style type="text/css">
        .SideNavi li#sideNav6 a {
            background-color: #6D6B6A;color:#DADF00;
        }
    </style>
    <script language="JavaScript">
        function PdfCheck(parts, sir_id){
            parts.action='user_sign_pdf.php';
            parts.target = '_self';
            parts.sir_id.value = sir_id;
            parts.submit();
            return true;
        }

        function SearchCheck(parts) {
            year = parts.sr_st_date_year;
            month = parts.sr_st_date_month;
            day = parts.sr_st_date_day;
            if (refNum(year, month, day) == false) {
                parts.sr_st_date_year.focus();
                return false;
            }
            year = parts.sr_ed_date_year;
            month = parts.sr_ed_date_month;
            day = parts.sr_ed_date_day;
            if (refNum(year, month, day) == false) {
                parts.sr_ed_date_year.focus();
                return false;
            }
            parts.action='users_sign_main.php';
            parts.or_sort.value = '';
            parts.target = '_self';
            parts.submit();
            return true;
        }

        function refNum(year, month, day, parts) {
            if (year.value != '' && month.value != '' && day.value != '') {
                if (DateCheck_1(year.value + '-' + month.value + '-' + day.value) == false) {
                    alert("日付が正しくありません");
                    year.focus();
                    return false;
                }
            }
            return true;
        }
    </script>
</head>

<body>

<div id="wrapAll">

    <!-- heade.tpl -->

<!-- #topH1  -->
<div id="topH1">
<div class="boxHead">
<a href="./index.php"><img src="../images/logo.jpg" height="50"></a>
</div>
<p id="nameP">2025/05/15（木）</p>
<h2>スタッフ用ツール　　　
<select name="Studio" id="selStudios" onchange="document.move_room.room_id.value=this.options[this.selectedIndex].value;document.move_room.submit();">
  <option value="1">渋谷校</option>
  <option value="9">渋谷2nd校</option>
  <option value="10">渋谷SCRAMBLE</option>
  <option value="11" selected="">EnSTUDIO</option>
  <option value="2">横浜校</option>
</select>
　　平野さん</h2>

<div class="div_tab clearfix mb05">
<span class="btntab1">メインメニュー</span>
<span class="btntab2"><a href="rental_yoyaku_main.php">貸しスタジオ</a></span>
</div>

<ul>
  <li id="btnLogout"><a href="../logout.php">ログアウト</a></li>
</ul>

<form action="move_room.php" name="move_room" method="post">
<input type="hidden" name="room_id" value="">
<input type="hidden" name="login_mode" value="login">
</form>

</div>
<!--/ #topH1 -->

<!-- #glnavi -->
<!--/ #glnavi -->

<!-- /heade.tpl -->

    <div class="bread"></div>

    <div id="wrapMain" class="clearfix">

        <div id="content" class="clearfix">

            <h2><span>会員署名一覧</span></h2>

            <form action="users_sign_main.php" method="post" name="searchform">
                <div class="boxSearch2 clearfix mb10">
                    <dl class="flDL-1 ddL searchDL clearfix">
                        <dt>
                            <img width="18" height="18" alt="検索" src="images/icon-search.gif">
                        </dt>
                        <dd style="font-weight:bold;">基本検索</dd>
                        <dd class="clearfix">
                            会員番号：<input type="text" name="sr_code" class="txt-S" maxlength="200" value="">
                            　　会員氏名：<input type="text" name="sr_kanji" class="txt-S" maxlength="200" value="">
                            　　会員カナ：<input type="text" name="sr_kana" class="txt-S" maxlength="200" value="">
                            　　性別：
                            <select name="sr_sex">
                                <option value="">全て</option>
                                                                <option value="1">男</option>
                                <option value="2">女</option>
                            </select>
                            　　区分：
                            <select name="sr_kbn">
                                <option value="">全て</option>
                                                                <option value="1">退会</option>
                                                                <option value="2">コース変更</option>
                                                                <option value="3">休会</option>
                                                                <option value="4">ウォータースタンド解約</option>
                                                                <option value="5">在籍変更</option>
                                                            </select>
                            　　署名状態：
                            <select name="sr_status">
                                <option value="">全て</option>
                                                                <option value="1">未署名</option>
                                <option value="2">署名済</option>
                            </select>
                        </dd>
                        <dd class="clearfix">
                            署名日：
                            <select name="sr_st_date_year" onchange="refNum(sr_st_date_year,sr_st_date_month,sr_st_date_day)">
<option value="">
</option><option value="2012">2012
</option><option value="2013">2013
</option><option value="2014">2014
</option><option value="2015">2015
</option><option value="2016">2016
</option><option value="2017">2017
</option><option value="2018">2018
</option><option value="2019">2019
</option><option value="2020">2020
</option><option value="2021">2021
</option><option value="2022">2022
</option><option value="2023">2023
</option><option value="2024">2024
</option><option value="2025">2025
</option><option value="2026">2026
</option></select>年<select name="sr_st_date_month" onchange="refNum(sr_st_date_year,sr_st_date_month,sr_st_date_day)">
<option value="">
</option><option value="01">1
</option><option value="02">2
</option><option value="03">3
</option><option value="04">4
</option><option value="05">5
</option><option value="06">6
</option><option value="07">7
</option><option value="08">8
</option><option value="09">9
</option><option value="10">10
</option><option value="11">11
</option><option value="12">12
</option></select>月
<select name="sr_st_date_day" onchange="refNum(sr_st_date_year,sr_st_date_month,sr_st_date_day)">
<option value="">
</option><option value="01">1
</option><option value="02">2
</option><option value="03">3
</option><option value="04">4
</option><option value="05">5
</option><option value="06">6
</option><option value="07">7
</option><option value="08">8
</option><option value="09">9
</option><option value="10">10
</option><option value="11">11
</option><option value="12">12
</option><option value="13">13
</option><option value="14">14
</option><option value="15">15
</option><option value="16">16
</option><option value="17">17
</option><option value="18">18
</option><option value="19">19
</option><option value="20">20
</option><option value="21">21
</option><option value="22">22
</option><option value="23">23
</option><option value="24">24
</option><option value="25">25
</option><option value="26">26
</option><option value="27">27
</option><option value="28">28
</option><option value="29">29
</option><option value="30">30
</option><option value="31">31
</option></select>日
                            ～
                            <select name="sr_ed_date_year" onchange="refNum(sr_ed_date_year,sr_ed_date_month,sr_ed_date_day)">
<option value="">
</option><option value="2012">2012
</option><option value="2013">2013
</option><option value="2014">2014
</option><option value="2015">2015
</option><option value="2016">2016
</option><option value="2017">2017
</option><option value="2018">2018
</option><option value="2019">2019
</option><option value="2020">2020
</option><option value="2021">2021
</option><option value="2022">2022
</option><option value="2023">2023
</option><option value="2024">2024
</option><option value="2025">2025
</option><option value="2026">2026
</option></select>年<select name="sr_ed_date_month" onchange="refNum(sr_ed_date_year,sr_ed_date_month,sr_ed_date_day)">
<option value="">
</option><option value="01">1
</option><option value="02">2
</option><option value="03">3
</option><option value="04">4
</option><option value="05">5
</option><option value="06">6
</option><option value="07">7
</option><option value="08">8
</option><option value="09">9
</option><option value="10">10
</option><option value="11">11
</option><option value="12">12
</option></select>月
<select name="sr_ed_date_day" onchange="refNum(sr_ed_date_year,sr_ed_date_month,sr_ed_date_day)">
<option value="">
</option><option value="01">1
</option><option value="02">2
</option><option value="03">3
</option><option value="04">4
</option><option value="05">5
</option><option value="06">6
</option><option value="07">7
</option><option value="08">8
</option><option value="09">9
</option><option value="10">10
</option><option value="11">11
</option><option value="12">12
</option><option value="13">13
</option><option value="14">14
</option><option value="15">15
</option><option value="16">16
</option><option value="17">17
</option><option value="18">18
</option><option value="19">19
</option><option value="20">20
</option><option value="21">21
</option><option value="22">22
</option><option value="23">23
</option><option value="24">24
</option><option value="25">25
</option><option value="26">26
</option><option value="27">27
</option><option value="28">28
</option><option value="29">29
</option><option value="30">30
</option><option value="31">31
</option></select>日
                        </dd>
                    </dl>
                    <ul class="flUl-2 mb05 clearfix">
                        <li class="txt">
                            <a href="users_sign_main.php">検索条件をクリア</a>
                        </li>
                        <li class="btnBluSB btnM">
                            <span>
                                <input type="button" name="search" value="検　索" onclick="SearchCheck(this.form);">
                            </span>
                        </li>
                    </ul>
                </div>
                <input type="hidden" name="or_sort" value="">
            </form>

            <form action="users_sign_main.php" method="post" name="sort_form">
                <input type="hidden" name="or_sort" value="">
                <input type="hidden" name="stpos" value="1">            </form>
        <div>

        <table summary="一覧" class="newsTbl">
            <colgroup class="td10"></colgroup>
            <colgroup class="td07"></colgroup>
            <colgroup class="td10"></colgroup>
            <colgroup class="td10"></colgroup>
            <colgroup class="td15"></colgroup>
            <colgroup class="td07"></colgroup>
            <colgroup class="td07"></colgroup>
            <tbody><tr>
                <th scope="col">
                    区分
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='1';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='2';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">
                    会員番号
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='3';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='4';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">
                    会員名
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='5';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='6';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">
                    フリガナ
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='7';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='8';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">
                    署名日
                    <span style="float: right; font-size: 90%;">
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='9';document.sort_form.submit();">▼</a>&nbsp;
                        <a href="javascript:void(0);" onclick="document.sort_form.or_sort.value='10';document.sort_form.submit();">▲</a>
                    </span>
                </th>
                <th scope="col">電子証明</th>
                <th scope="col"></th>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001024</td>
                <td>赤土  美穂</td>
                <td>シャクド  ミホ</td>
                <td>2025年05月14日 19時25分47秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3993);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3993; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND001004</td>
                <td>藤澤  美夢</td>
                <td>フジサワ  ミユ</td>
                <td>2025年05月14日 15時36分11秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3991);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3991; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>ND000392</td>
                <td>久保  衣璃</td>
                <td>クボ  イリ</td>
                <td>2025年05月11日 19時42分41秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3981);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3981; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND000889</td>
                <td>高田  風花</td>
                <td>タカダ  フウカ</td>
                <td>2025年05月11日 14時45分25秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3979);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3979; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001203</td>
                <td>佐藤  梨央</td>
                <td>サトウ  リオ</td>
                <td>2025年05月10日 20時21分39秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3972);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3972; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND000475</td>
                <td>中谷  さくら</td>
                <td>ナカタニ  サクラ</td>
                <td>2025年05月10日 18時38分32秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3969);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3969; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001212</td>
                <td>進藤  心音</td>
                <td>シンドウ  ココネ</td>
                <td>2025年05月10日 18時10分26秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3965);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3965; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND001078</td>
                <td>五十嵐  心愛</td>
                <td>イガラシ  ミア</td>
                <td>2025年05月10日 17時50分19秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3962);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3962; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>ND000089</td>
                <td>竹内  咲貴</td>
                <td>タケウチ  サキ</td>
                <td>2025年05月10日 17時41分39秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3961);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3961; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>在籍変更</td>
                <td>ND001210</td>
                <td>神谷  幸音</td>
                <td>カミヤ  ユキネ</td>
                <td>2025年05月10日 16時38分00秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3959);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3959; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001210</td>
                <td>神谷  幸音</td>
                <td>カミヤ  ユキネ</td>
                <td>2025年05月10日 16時36分57秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3958);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3958; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND000265</td>
                <td>河合  美音</td>
                <td>カワイ  ミオン</td>
                <td>2025年05月10日 15時33分53秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3954);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3954; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>ND001065</td>
                <td>工藤  菜々</td>
                <td>クドウ  ナナ</td>
                <td>2025年05月10日 15時22分27秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3953);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3953; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND000962</td>
                <td>首藤  京花</td>
                <td>シュトウ  キョウカ</td>
                <td>2025年05月10日 15時15分27秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3950);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3950; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND000937</td>
                <td>小島  莉空</td>
                <td>コジマ  リク</td>
                <td>2025年05月10日 15時07分14秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3949);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3949; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>在籍変更</td>
                <td>ND000937</td>
                <td>小島  莉空</td>
                <td>コジマ  リク</td>
                <td>2025年05月10日 15時02分19秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3948);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3948; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001218</td>
                <td>鈴木  日心</td>
                <td>スズキ  ニコ</td>
                <td>2025年05月10日 14時54分20秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3947);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3947; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND000885</td>
                <td>高橋  愛生</td>
                <td>タカハシ  メイ</td>
                <td>2025年05月10日 14時58分13秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3946);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3946; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>ND000733</td>
                <td>細川  希</td>
                <td>ホソカワ  ハルカ</td>
                <td>2025年05月10日 14時23分08秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3945);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3945; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND001180</td>
                <td>横山  るな</td>
                <td>ヨコヤマ  ルナ</td>
                <td>2025年05月10日 13時15分23秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3943);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3943; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001209</td>
                <td>小見山  卓</td>
                <td>コミヤマ  タク</td>
                <td>2025年05月10日 12時13分35秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3941);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3941; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND000055</td>
                <td>内藤  典子</td>
                <td>ナイトウ  ノリコ</td>
                <td>2025年05月09日 21時35分33秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3939);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3939; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001202</td>
                <td>野口  美来</td>
                <td>ノグチ  ミク</td>
                <td>2025年05月09日 17時31分54秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3931);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3931; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND000945</td>
                <td>高後  楓</td>
                <td>コウゴ  カエデ</td>
                <td>2025年05月09日 17時31分36秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3930);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3930; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001141</td>
                <td>佐藤  柚希</td>
                <td>サトウ  ユズキ</td>
                <td>2025年05月09日 17時14分51秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3929);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3929; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND000745</td>
                <td>小早川  こころ</td>
                <td>コバヤカワ  ココロ</td>
                <td>2025年05月09日 16時47分22秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3928);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3928; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND000899</td>
                <td>川本  葵世</td>
                <td>カワモト  キセ</td>
                <td>2025年05月09日 14時38分23秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3922);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3922; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND001052</td>
                <td>ゴン  ティンシュエン</td>
                <td>ゴン  ティンシュエン</td>
                <td>2025年05月08日 19時40分10秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3908);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3908; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001092</td>
                <td>渥見  絢</td>
                <td>アツミ  ジュン</td>
                <td>2025年05月08日 18時43分01秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3905);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3905; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>休会</td>
                <td>ND000005</td>
                <td>水野  芽桜</td>
                <td>ミズノ  メオ</td>
                <td>2025年05月08日 17時59分48秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3898);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3898; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>休会</td>
                <td>ND000005</td>
                <td>水野  芽桜</td>
                <td>ミズノ  メオ</td>
                <td>2025年05月08日 18時00分40秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3897);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3897; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND000597</td>
                <td>小西  萌乃香</td>
                <td>コニシ  ホノカ</td>
                <td>2025年05月08日 16時00分18秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3887);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3887; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND000935</td>
                <td>大木  実楓</td>
                <td>オオキ  ミフミ</td>
                <td>2025年05月08日 14時10分28秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3884);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3884; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND001216</td>
                <td>糸川  まゆり</td>
                <td>イトカワ  マユリ</td>
                <td>2025年05月08日 14時03分51秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3883);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3883; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001192</td>
                <td>高橋  美結</td>
                <td>タカハシ  ミユ</td>
                <td>2025年05月07日 18時48分59秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3872);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3872; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND000243</td>
                <td>西尾  菫</td>
                <td>ニシオ  スミレ</td>
                <td>2025年05月07日 18時21分40秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3868);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3868; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>ND000818</td>
                <td>沖  真彩</td>
                <td>オキ  マアヤ</td>
                <td>2025年05月07日 17時08分00秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3865);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3865; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND001091</td>
                <td>高橋  みつき</td>
                <td>タカハシ  ミツキ</td>
                <td>2025年05月07日 14時29分17秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3857);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3857; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND000664</td>
                <td>Tosbath  Taline</td>
                <td>トスバース  タリーヌ</td>
                <td>2025年05月06日 20時49分29秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3855);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3855; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND000921</td>
                <td>金子  貴暉</td>
                <td>カネコ  タカキ</td>
                <td>2025年05月06日 19時15分08秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3852);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3852; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>ND000923</td>
                <td>宮城  月</td>
                <td>ミヤギ  ルナ</td>
                <td>2025年05月06日 15時06分38秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3842);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3842; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND000959</td>
                <td>鈴木  るり子</td>
                <td>スズキ  ルリコ</td>
                <td>2025年05月06日 14時09分27秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3840);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3840; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND000820</td>
                <td>池田  皆友</td>
                <td>イケダ  ミナト</td>
                <td>2025年05月05日 17時31分22秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3831);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3831; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND000577</td>
                <td>溝狹  蒼葉</td>
                <td>ミゾハサミ  アオバ</td>
                <td>2025年05月04日 14時45分39秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3822);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3822; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND000997</td>
                <td>佐藤  未侑</td>
                <td>サトウ  ミユウ</td>
                <td>2025年05月04日 14時38分58秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3821);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3821; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>コース変更</td>
                <td>ND001216</td>
                <td>糸川  まゆり</td>
                <td>イトカワ  マユリ</td>
                <td>2025年05月04日 12時57分22秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3817);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3817; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>コース変更</td>
                <td>ND001133</td>
                <td>増田  愛</td>
                <td>マスダ  イト</td>
                <td>2025年05月04日 11時42分16秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3813);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3813; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND001250</td>
                <td>辰己  宇未</td>
                <td>タツミ  ウミ</td>
                <td>2025年05月01日 16時25分47秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3774);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3774; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr>
                <td>退会</td>
                <td>ND001148</td>
                <td>Pino  Mary</td>
                <td>ピノ  マリ</td>
                <td>2025年04月30日 20時17分21秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3770);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3770; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                        <tr class="tr_another">
                <td>退会</td>
                <td>ND000993</td>
                <td>富樫  祥里</td>
                <td>トガシ  ショウリ</td>
                <td>2025年04月30日 16時16分51秒</td>
                <td class="txtC">
                                        <input id="btn-pdf-download" type="button" name="download" value="ダウンロード" onclick="PdfCheck(document.pdf_form, 3762);">
                                    </td>
                <td class="txtC">
                    <ul class="flUl-c">
                        <li class="btnGRsb" style="margin:1px;">
                            <span>
                                <input type="button" value="詳細" onclick="document.edit_form.sir_id.value=3762; document.edit_form.submit();">
                            </span>
                        </li>
                    </ul>
                </td>
            </tr>
                    </tbody></table>

        <form action="users_sign_conf.php" method="post" name="edit_form">
            <input type="hidden" name="sir_id" value="">
            <input type="hidden" name="or_sort" value="">
            <input type="hidden" name="stpos" value="1">        </form>

        <form action="" method="post" name="next_page">
            <input type="hidden" name="stpos" value="1">              <table border="0" cellspacing="0" cellpadding="2" width="700" class="px12">
    <tbody><tr>
      <td width="150">745 件中　1 - 50 件</td>
      <td width="550">
Page: <span class="px14"><b>1</b></span> 
<a href="javascript:SetStartNo2(51);">2</a> 
<a href="javascript:SetStartNo2(101);">3</a> 
<a href="javascript:SetStartNo2(151);">4</a> 
<a href="javascript:SetStartNo2(201);">5</a> 
<a href="javascript:SetStartNo2(251);">6</a> 
<a href="javascript:SetStartNo2(301);">7</a> 
<a href="javascript:SetStartNo2(351);">8</a> 
<a href="javascript:SetStartNo2(401);">9</a> 
<a href="javascript:SetStartNo2(451);">10</a> 
<input type="submit" value=">>" onclick="return SetStartNo( this.form , 51 );" class="btn_nosize">
      </td>
    </tr>
  </tbody></table>
  <script language="JavaScript">
  <!--
  function SetStartNo(parts,no) {
       parts.stpos.value = no;
       return true;
  }
  function SetStartNo2(no) {
       document.next_page.stpos.value = no;
       document.next_page.submit();
  }
  //-->
  </script>
            <input type="hidden" name="or_sort" value="">
        </form>

        <form action="" name="pdf_form" method="post" enctype="multipart/form-data">
            <input type="hidden" name="sir_id" value="">
        </form>
    </div>

    <div class="gotop">
        <p><a href="#">ページトップへ</a></p>
    </div>
</div>

<div id="sideLeft">
    <!-- left_menu.tpl -->
<h3>会員管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav01" class="btm"><a href="nyukai_main.php">入会申込み</a></li>
    <li id="sideNav1" class="btm"><a href="user_mnt.php">会員新規登録</a></li>
    <li id="sideNav2" class="btm"><a href="user_main.php">会員一覧</a></li>
    <li id="sideNav3" class="btm"><a href="javascript:void(0);" onclick="document.frm_cardread.page_mode.value='1';document.frm_cardread.submit();">会員カードNo登録</a></li>
    <li id="sideNav4" class="btm"><a href="mail_haishin.php">会員メール配信</a></li>
    <li id="sideNav6" class="btm"><a href="users_sign_main.php">会員署名一覧</a></li>
  </ul>
</div>
<h3>レッスン管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav30" class="btm"><a href="jyuko_search.php">レッスン受講一覧</a></li>
    <li id="sideNav31" class="btm"><a href="taiken_search.php">体験レッスン管理</a></li>
    <li id="sideNav32" class="btm"><a href="user_oshirase_main.php">My Pageお知らせ登録</a></li>
  </ul>
</div>
<h3>月謝管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav11" class="btm"><a href="course_ichiran.php">受講可能コース一覧</a></li>
    <li id="sideNav10" class="btm"><a href="gessya_search.php">会員月謝一覧</a></li>
  </ul>
</div>
<h3>販売管理</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav40" class="btm"><a href="javascript:void(0);" onclick="document.frm_cardread.page_mode.value='2';document.frm_cardread.submit();">商品販売</a></li>
    <li id="sideNav43" class="btm"><a href="pos_search.php">販売履歴一覧</a></li>
    <li id="sideNav44" class="btm"><a href="kinsyu_hyo.php">金種表</a></li>
    <li id="sideNav42" class="btm"><a href="pos_settei.php">レジ設定</a></li>
    <li id="sideNav41" class="btm"><a href="shohin_add_mnt.php">商品登録</a></li>
  </ul>
</div>
<h3>売上集計</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav60" class="btm"><a href="syukei_uriage.php">全体売上集計</a></li>
    <li id="sideNav61" class="btm"><a href="syukei_course.php">コース別売上集計</a></li>
    <li id="sideNav62" class="btm"><a href="syukei_shohin.php">商品別売上集計</a></li>
    <li id="sideNav65" class="btm"><a href="syukei_studio.php">スタジオ収支進捗表</a></li>
    <li id="sideNav68" class="btm"><a href="studio_transition.php">スタジオ推移表</a></li>
    <li id="sideNav66" class="btm"><a href="syukei_uriage_yoso.php">売上要素分解表</a></li>
  </ul>
</div>
<h3>日報掲示板登録</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav70" class="btm"><a href="nippo_room.php">教室日報</a></li>
    <li id="sideNav71" class="btm"><a href="nippo_staff.php">スタッフ日報</a></li>
    <!-- <li id="sideNav72" class="btm"><a href="keijiban_main.php">掲示板</a></li> -->
    <li id="sideNav73" class="btm"><a href="nippo_front.php">フロント業務日報</a></li>
  </ul>
</div>
<h3>管理機能</h3>
<div class="SideNavi">
  <ul>
    <li id="sideNav20" class="btm"><a href="getsuji_kakutei.php">月次確定処理</a></li>
	<li id="sideNav23" class="btm"><a href="point_search.php">付与ポイント管理</a></li>
    <li id="sideNav21" class="btm"><a href="holiday_main.php">休日カレンダー</a></li>
    <li id="sideNav22" class="btm"><a href="minochk_hiduke.php">未納ﾁｪｯｸ開始日設定</a></li>
  </ul>
</div>
<form action="card_write.php" name="frm_cardread" method="post" target="_self">
<input type="hidden" name="mode" value="">
<input type="hidden" name="page_mode" value="">
</form>
<!-- /left_menu.tpl -->
</div>

<!-- footer.tpl -->
<div id="footer-space"></div><!-- footer固定用スペース -->
</div>
<!--/ #wrapAll -->

<!-- footer -->
<div id="wrapFoot">
<div id="footer">
</div>
</div>
<!--/ footer -->
<!-- /footer.tpl -->



</div></body></html>