#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple script to capture the page source of the teacher list page
"""

import os
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Import configuration
from config_teacher import CREDENTIALS

# Update the variables to match the config_teacher format
USERNAME = CREDENTIALS["username"]
PASSWORD = CREDENTIALS["password"]
BASE_URL = "https://www.en-system.net/admin/"
LOGIN_URL = BASE_URL
TEACHER_LIST_URL = BASE_URL + "tool/teacher_main.php"

def setup_logging():
    """Set up logging configuration"""
    logger = logging.getLogger("PageCapture")
    logger.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    console_handler.setFormatter(formatter)
    
    # Add handlers
    logger.addHandler(console_handler)
    
    return logger

def initialize_driver():
    """Initialize the Selenium WebDriver"""
    # Set Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    
    # Add performance options
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-gpu")
    
    # Always enable images
    chrome_prefs = {
        "profile.default_content_setting_values": {
            "images": 1  # 1 = allow, 2 = block
        }
    }
    chrome_options.add_experimental_option("prefs", chrome_prefs)
    
    # Initialize driver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    driver.set_page_load_timeout(30)
    
    return driver

def login(driver, logger):
    """Log into the system"""
    logger.info("Starting login process")
    
    # Navigate to login page
    driver.get(LOGIN_URL)
    logger.info(f"Navigated to login page: {LOGIN_URL}")
    
    # Wait for page to fully load
    time.sleep(1)
    
    # Fill login form
    login_script = f"""
    // Find the username field
    var usernameField = document.querySelector('input[name="login_id"]');
    if (usernameField) {{
        usernameField.value = "{USERNAME}";
        console.log("Set username to {USERNAME}");
    }} else {{
        console.error("Username field not found");
    }}
    
    // Find the password field
    var passwordField = document.querySelector('input[name="login_pass"]');
    if (passwordField) {{
        passwordField.value = "{PASSWORD}";
        console.log("Set password");
    }} else {{
        console.error("Password field not found");
    }}
    
    return {{
        username: usernameField ? true : false,
        password: passwordField ? true : false
    }};
    """
    
    # Execute the script
    form_results = driver.execute_script(login_script)
    logger.info(f"Form field detection results: {form_results}")
    
    # Find and click login button
    wait = WebDriverWait(driver, 10)
    selectors = [
        (By.CSS_SELECTOR, "input[type='submit']"),
        (By.XPATH, "//button[contains(text(), 'ログイン')]"),
        (By.XPATH, "//input[@value='ログイン']"),
        (By.CLASS_NAME, "btnLogin")
    ]
    
    for selector_type, selector in selectors:
        try:
            login_button = wait.until(EC.element_to_be_clickable((selector_type, selector)))
            logger.info(f"Found login button using selector: {selector}")
            login_button.click()
            logger.info("Clicked login button")
            break
        except:
            continue
    
    # Wait for login to complete
    success_indicators = [
        "//body[contains(., '本部用ツール')]",
        "//a[contains(@href, 'logout.php')]",
        "//div[contains(@class, 'header') and contains(., '本部')]"
    ]
    
    login_success = False
    for indicator in success_indicators:
        try:
            wait.until(EC.presence_of_element_located((By.XPATH, indicator)))
            login_success = True
            logger.info(f"Login verified with indicator: {indicator}")
            break
        except:
            continue
    
    if login_success:
        logger.info("Login completed successfully")
        return True
    else:
        logger.error("Login failed")
        return False

def navigate_to_teacher_list(driver, logger):
    """Navigate to the teacher list page"""
    logger.info(f"Navigating to teacher list page: {TEACHER_LIST_URL}")
    
    # Navigate to teacher list URL
    driver.get(TEACHER_LIST_URL)
    
    # Wait for page to load
    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
    
    # Save screenshot
    driver.save_screenshot("teacher_list_before_search.png")
    logger.info("Saved screenshot of teacher list page before search")
    
    # Save page source
    with open("teacher_list_before_search.html", "w", encoding="utf-8") as f:
        f.write(driver.page_source)
    logger.info("Saved page source of teacher list page before search")
    
    # Click search button to display teachers
    try:
        search_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable(
            (By.XPATH, "//input[@type='submit' and @name='search' and @value='検　索']")
        ))
        search_button.click()
        logger.info("Clicked search button to display teachers")
        time.sleep(2)
        
        # Save screenshot after search
        driver.save_screenshot("teacher_list_after_search.png")
        logger.info("Saved screenshot of teacher list page after search")
        
        # Save page source after search
        with open("teacher_list_after_search.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        logger.info("Saved page source of teacher list page after search")
    except Exception as e:
        logger.error(f"Error clicking search button: {str(e)}")
        
        # Try to find the table anyway
        logger.info("Trying to find teacher table without clicking search")
        
    # Try to find the teacher table
    try:
        table = WebDriverWait(driver, 10).until(EC.presence_of_element_located(
            (By.XPATH, "//table[contains(@class, 'list_tbl') or contains(@class, 'newsTbl')]")
        ))
        logger.info("Found teacher table")
        
        # Get table HTML
        table_html = table.get_attribute("outerHTML")
        with open("teacher_table.html", "w", encoding="utf-8") as f:
            f.write(table_html)
        logger.info("Saved teacher table HTML")
        
        # Count rows
        rows = table.find_elements(By.TAG_NAME, "tr")
        logger.info(f"Found {len(rows)} rows in teacher table (including header)")
        
        return True
    except Exception as e:
        logger.error(f"Could not find teacher table: {str(e)}")
        
        # Try to find any tables
        tables = driver.find_elements(By.TAG_NAME, "table")
        logger.info(f"Found {len(tables)} tables on the page")
        
        for i, table in enumerate(tables):
            try:
                table_html = table.get_attribute("outerHTML")
                with open(f"table_{i+1}.html", "w", encoding="utf-8") as f:
                    f.write(table_html)
                logger.info(f"Saved table {i+1} HTML")
            except:
                logger.error(f"Could not save table {i+1} HTML")
        
        return False

def main():
    """Main function"""
    logger = setup_logging()
    
    logger.info("Starting page capture")
    
    # Initialize WebDriver
    driver = None
    try:
        driver = initialize_driver()
        
        # Login
        if not login(driver, logger):
            logger.error("Login failed, cannot continue")
            return
        
        # Navigate to teacher list
        if not navigate_to_teacher_list(driver, logger):
            logger.error("Failed to navigate to teacher list, but saved available page content")
        
        logger.info("Page capture completed")
        
    except Exception as e:
        logger.error(f"Page capture process failed: {str(e)}")
    finally:
        # Close WebDriver
        if driver:
            driver.quit()
            logger.info("WebDriver closed")

if __name__ == "__main__":
    main()
