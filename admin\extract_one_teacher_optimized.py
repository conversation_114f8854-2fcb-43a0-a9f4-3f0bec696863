#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Optimized script to extract detailed data for a single teacher from EN Dance System Admin
This script focuses on performance optimization for extracting teacher data
"""

import os
import sys
import time
import json
import logging
import traceback
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    ElementNotInteractableException,
    StaleElementReferenceException
)
from webdriver_manager.chrome import ChromeDriverManager

# Import configuration
from config_teacher import CREDENTIALS
from config_buttons import BUTTON_TYPES, BUTTON_CONFIG, VERIFIED_BUTTON_CONFIG

# Update the variables to match the config_teacher format
USERNAME = CREDENTIALS["username"]
PASSWORD = CREDENTIALS["password"]
BASE_URL = "https://www.en-system.net/admin/"
LOGIN_URL = BASE_URL
TEACHER_LIST_URL = BASE_URL + "tool/teacher_main.php"

# Global cache for page structure
PAGE_STRUCTURE_CACHE = {}

class OptimizedTeacherExtractor:
    """
    Optimized class to extract detailed data for teachers
    """
    
    def __init__(self, output_dir=None, teacher_index=0, save_screenshots=True, debug_mode=True):
        """
        Initialize the extractor
        
        Args:
            output_dir: Directory to save output (default: single_teacher_{timestamp})
            teacher_index: Index of the teacher to extract (0 = first teacher on the page)
            save_screenshots: Whether to save screenshots (default: True)
            debug_mode: Whether to enable debug mode with more logging (default: True)
        """
        # Set up output directory
        if not output_dir:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f"single_teacher_{timestamp}"
        
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
        # Configuration
        self.teacher_index = teacher_index
        self.button_types = BUTTON_TYPES
        self.save_screenshots = save_screenshots
        self.debug_mode = debug_mode
        
        # Set up logging
        self.log_file = os.path.join(output_dir, "extraction.log")
        self._setup_logging()
        
        # Initialize stats
        self.stats = {
            "teacher_id": None,
            "teacher_name": None,
            "extraction_start": datetime.now(),
            "button_results": {button_type: {"success": False, "fields_found": 0, "tables_found": 0} 
                              for button_type in self.button_types},
            "errors": []
        }
        
        # WebDriver
        self.driver = None
        
        # Performance tracking
        self.timings = {
            "login": 0,
            "navigate_to_teacher_list": 0,
            "find_teacher": 0,
            "process_teacher": 0,
            "button_timings": {button_type: 0 for button_type in self.button_types}
        }
        
        self.logger.info("Initialized OptimizedTeacherExtractor")
        self.logger.info(f"Output directory: {self.output_dir}")
        self.logger.info(f"Teacher index: {self.teacher_index}")
        self.logger.info(f"Save screenshots: {self.save_screenshots}")
        self.logger.info(f"Debug mode: {self.debug_mode}")
    
    def _setup_logging(self):
        """Set up logging configuration"""
        self.logger = logging.getLogger("OptimizedTeacherExtractor")
        
        # Set logging level based on debug mode
        log_level = logging.DEBUG if self.debug_mode else logging.INFO
        self.logger.setLevel(log_level)
        
        # Check if the logger already has handlers to avoid duplicates
        if not self.logger.handlers:
            # File handler
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(log_level)
            
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(log_level)
            
            # Formatter
            formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # Add handlers
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
        else:
            # If handlers already exist, just update their log level
            for handler in self.logger.handlers:
                handler.setLevel(log_level)
    
    def _log_error(self, message, error=None):
        """Log an error with optional exception details"""
        if error:
            error_msg = f"{message}: {str(error)}"
            if self.debug_mode:
                stack_trace = traceback.format_exc()
                self.logger.error(f"{error_msg}\n{stack_trace}")
            else:
                self.logger.error(error_msg)
            self.stats["errors"].append(error_msg)
        else:
            self.logger.error(message)
            self.stats["errors"].append(message)
    
    def _initialize_driver(self):
        """Initialize the Selenium WebDriver"""
        try:
            # Set Chrome options
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-infobars")
            
            # Add performance options
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-gpu")
            
            # Always enable images for the 詳細 page
            # We'll control image loading more precisely in the code
            chrome_prefs = {
                "profile.default_content_setting_values": {
                    "images": 1  # 1 = allow, 2 = block
                }
            }
            chrome_options.add_experimental_option("prefs", chrome_prefs)
            
            # Initialize driver
            self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
            self.driver.set_page_load_timeout(30)
            
            # Create a WebDriverWait instance
            self.wait = WebDriverWait(self.driver, 10)
            
            return True
        except Exception as e:
            self._log_error("Failed to initialize WebDriver", e)
            return False
    
    def _save_screenshot(self, directory, filename):
        """Save a screenshot to the specified directory if save_screenshots is enabled"""
        if not self.save_screenshots and not self.debug_mode:
            return None
            
        try:
            os.makedirs(directory, exist_ok=True)
            screenshot_path = os.path.join(directory, filename)
            self.driver.save_screenshot(screenshot_path)
            if self.debug_mode:
                self.logger.debug(f"Saved screenshot to {screenshot_path}")
            return screenshot_path
        except Exception as e:
            self._log_error(f"Failed to save screenshot {filename}", e)
            return None
    
    def _save_page_source(self, directory, filename):
        """Save the page source to the specified directory if in debug mode"""
        if not self.debug_mode:
            return None
            
        try:
            os.makedirs(directory, exist_ok=True)
            html_path = os.path.join(directory, filename)
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            self.logger.debug(f"Saved page source to {html_path}")
            return html_path
        except Exception as e:
            self._log_error(f"Failed to save page source {filename}", e)
            return None
    
    def _save_data_as_json(self, directory, filename, data):
        """Save data as JSON to the specified directory"""
        try:
            os.makedirs(directory, exist_ok=True)
            json_path = os.path.join(directory, filename)
            
            # Convert datetime objects to strings
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, datetime):
                        data[key] = value.strftime("%Y-%m-%d %H:%M:%S")
            
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            if self.debug_mode:
                self.logger.debug(f"Saved JSON data to {json_path}")
            return json_path
        except Exception as e:
            self._log_error(f"Failed to save JSON data {filename}", e)
            return None
    
    def _download_teacher_images(self, button_dir, form_fields):
        """Download teacher images from the 詳細 page"""
        try:
            self.logger.info("Downloading teacher images")
            
            # Create images directory
            images_dir = os.path.join(button_dir, "images")
            os.makedirs(images_dir, exist_ok=True)
            
            # Get teacher ID from stats (more reliable) or form fields
            teacher_id = None
            if self.stats["teacher_id"]:
                teacher_id = self.stats["teacher_id"]
            elif "teacher_id" in form_fields and form_fields["teacher_id"]:
                teacher_id = form_fields["teacher_id"]
            
            if not teacher_id:
                self.logger.warning("Could not determine teacher ID for image download")
                return []
            
            # Log the teacher ID being used
            self.logger.info(f"Using teacher ID: {teacher_id} for image download")
            
            # Check if teacher_id is numeric
            try:
                # Try to convert to int to verify it's a valid numeric ID
                int(teacher_id)
            except ValueError:
                self.logger.warning(f"Teacher ID '{teacher_id}' is not numeric, this may cause issues with image URLs")
                
            # Check if the form fields contain a different teacher ID
            if "teacher_id" in form_fields and form_fields["teacher_id"] and form_fields["teacher_id"] != teacher_id:
                self.logger.warning(f"Form field teacher_id ({form_fields['teacher_id']}) differs from stats teacher_id ({teacher_id})")
                self.logger.info(f"Using stats teacher_id ({teacher_id}) for image download as it's more reliable")
            
            # Download images
            downloaded_images = []
            
            # Define image numbers to try
            image_numbers = ["1", "2", "3", "thumbnail_1", "thumbnail_2", "thumbnail_3"]
            base_url = "https://www.en-system.net/admin/tool/img_thumb.php?f=files/teacher/"
            
            # APPROACH 1: Browser-based approach (more reliable)
            self.logger.info("Using browser-based approach for teacher images")
            
            # Save the current URL to navigate back to after downloading images
            current_page_url = self.driver.current_url
            
            for img_num in ["1", "2", "3", "thumbnail_1"]:  # Focus on the main images
                try:
                    # Construct the image URL
                    img_url = f"{base_url}{teacher_id}_{img_num}.jpg"
                    filename = f"{teacher_id}_{img_num}.jpg"
                    
                    # Create a new tab for the image
                    self.driver.execute_script("window.open('', '_blank');")
                    self.driver.switch_to.window(self.driver.window_handles[1])
                    
                    # Navigate directly to the image URL
                    self.logger.info(f"Navigating directly to image URL: {img_url}")
                    self.driver.get(img_url)
                    time.sleep(2)  # Wait for the image to load
                    
                    # Take a screenshot of the page
                    screenshot_path = os.path.join(images_dir, f"{filename}")
                    self.driver.save_screenshot(screenshot_path)
                    
                    # Check if the file was saved and has a reasonable size
                    file_size = os.path.getsize(screenshot_path)
                    self.logger.info(f"Saved teacher image to {screenshot_path} (size: {file_size} bytes)")
                    
                    if file_size > 5000:  # Assume any file smaller than 5KB is probably not a valid image
                        downloaded_images.append(filename)
                    else:
                        self.logger.warning(f"Screenshot is too small ({file_size} bytes), might not be a valid image")
                        # Delete the small file
                        os.remove(screenshot_path)
                    
                    # Close the tab and switch back to the original tab
                    self.driver.close()
                    self.driver.switch_to.window(self.driver.window_handles[0])
                except Exception as e:
                    self.logger.warning(f"Error using browser to download image {img_num}: {str(e)}")
                    # Make sure we're back on the original tab
                    if len(self.driver.window_handles) > 1:
                        self.driver.close()
                        self.driver.switch_to.window(self.driver.window_handles[0])
            
            # Navigate back to the original page
            try:
                self.driver.get(current_page_url)
                time.sleep(1)  # Wait for the page to load
            except Exception as e:
                self.logger.warning(f"Error navigating back to original page: {str(e)}")
                
            # APPROACH 2: Direct download with requests using browser cookies (as fallback)
            if not downloaded_images:
                self.logger.info("Trying direct download approach with browser cookies")
                try:
                    import requests
                    from requests.packages.urllib3.exceptions import InsecureRequestWarning
                    requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
                    
                    # Get cookies from the browser
                    cookies = {cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()}
                    
                    # Set headers to mimic browser request
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Referer': self.driver.current_url
                    }
                    
                    for img_num in image_numbers:
                        try:
                            # Construct the image URL
                            img_url = f"{base_url}{teacher_id}_{img_num}.jpg"
                            filename = f"{teacher_id}_{img_num}.jpg"
                            
                            # Download the image
                            self.logger.info(f"Downloading image from URL: {img_url}")
                            response = requests.get(img_url, cookies=cookies, headers=headers, verify=False, timeout=10)
                            
                            if response.status_code == 200:
                                # Check if the response is an actual image
                                content_type = response.headers.get('content-type', '')
                                content_length = int(response.headers.get('content-length', 0))
                                
                                # Verify it's an image by content type and size
                                if ('image' in content_type or content_type == 'application/octet-stream') and content_length > 1000:
                                    image_path = os.path.join(images_dir, filename)
                                    with open(image_path, 'wb') as f:
                                        f.write(response.content)
                                    
                                    self.logger.info(f"Successfully downloaded teacher image: {filename} (size: {content_length} bytes)")
                                    downloaded_images.append(filename)
                                else:
                                    self.logger.warning(f"Response for {img_url} is not a valid image: {content_type}, size: {content_length} bytes")
                            else:
                                self.logger.warning(f"Failed to download image {filename}: HTTP {response.status_code}")
                        except Exception as e:
                            self.logger.warning(f"Failed to download image {img_num}: {str(e)}")
                except Exception as e:
                    self.logger.warning(f"Error in direct download approach: {str(e)}")
            
            # APPROACH 2: Extract image URLs directly from the page
            if not downloaded_images:
                self.logger.info("Trying to extract image URLs from the page")
                
                # Look for image elements that might be teacher photos
                image_elements = self.driver.find_elements(By.TAG_NAME, "img")
                self.logger.info(f"Found {len(image_elements)} image elements on the page")
                
                for i, img in enumerate(image_elements):
                    try:
                        src = img.get_attribute("src")
                        if not src:
                            continue
                        
                        # Look specifically for teacher image URLs
                        if "img_thumb.php" in src and "files/teacher" in src:
                            # Extract image number from URL if possible
                            import re
                            img_match = re.search(r'files/teacher/(\d+)_(\w+)\.jpg', src)
                            if img_match:
                                img_teacher_id = img_match.group(1)
                                img_num = img_match.group(2)
                                filename = f"{img_teacher_id}_{img_num}.jpg"
                            else:
                                filename = f"{teacher_id}_img_{i+1}.jpg"
                            
                            # Download image using requests with cookies
                            try:
                                import requests
                                cookies = {cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()}
                                headers = {
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                                    'Referer': self.driver.current_url
                                }
                                
                                response = requests.get(src, cookies=cookies, headers=headers, verify=False, timeout=10)
                                
                                if response.status_code == 200 and len(response.content) > 1000:
                                    image_path = os.path.join(images_dir, filename)
                                    with open(image_path, 'wb') as f:
                                        f.write(response.content)
                                    
                                    self.logger.info(f"Downloaded teacher image from page: {filename} (size: {len(response.content)} bytes)")
                                    downloaded_images.append(filename)
                            except Exception as e:
                                self.logger.warning(f"Failed to download image from src {src}: {str(e)}")
                    except Exception as e:
                        self.logger.warning(f"Failed to process image element {i}: {str(e)}")
            
            # APPROACH 3: Browser-based approach (fallback)
            if not downloaded_images:
                self.logger.info("Trying browser-based approach for teacher images")
                
                # Save the current URL to navigate back to after downloading images
                current_page_url = self.driver.current_url
                
                for img_num in ["2", "3"]:  # Focus on the main images mentioned by the user
                    try:
                        # Construct the image URL
                        img_url = f"{base_url}{teacher_id}_{img_num}.jpg"
                        filename = f"{teacher_id}_{img_num}.jpg"
                        
                        # Create a new tab for the image
                        self.driver.execute_script("window.open('', '_blank');")
                        self.driver.switch_to.window(self.driver.window_handles[1])
                        
                        # Navigate directly to the image URL
                        self.logger.info(f"Navigating directly to image URL: {img_url}")
                        self.driver.get(img_url)
                        time.sleep(2)  # Wait for the image to load
                        
                        # Take a screenshot of the page
                        screenshot_path = os.path.join(images_dir, f"{filename}")
                        self.driver.save_screenshot(screenshot_path)
                        
                        # Check if the file was saved and has a reasonable size
                        file_size = os.path.getsize(screenshot_path)
                        self.logger.info(f"Saved teacher image to {screenshot_path} (size: {file_size} bytes)")
                        
                        if file_size > 5000:  # Assume any file smaller than 5KB is probably not a valid image
                            downloaded_images.append(filename)
                        else:
                            self.logger.warning(f"Screenshot is too small ({file_size} bytes), might not be a valid image")
                            # Delete the small file
                            os.remove(screenshot_path)
                        
                        # Close the tab and switch back to the original tab
                        self.driver.close()
                        self.driver.switch_to.window(self.driver.window_handles[0])
                    except Exception as e:
                        self.logger.warning(f"Error using browser to download image {img_num}: {str(e)}")
                        # Make sure we're back on the original tab
                        if len(self.driver.window_handles) > 1:
                            self.driver.close()
                            self.driver.switch_to.window(self.driver.window_handles[0])
                
                # Navigate back to the original page
                try:
                    self.driver.get(current_page_url)
                    time.sleep(1)  # Wait for the page to load
                except Exception as e:
                    self.logger.warning(f"Error navigating back to original page: {str(e)}")
            
            # Define image fields (moved to the beginning of the method)
            image_fields = [
                "image_old_1", "image_old_2", "image_old_3", "image_old_3_detail",
                "image_old_thumbnail_1", "image_old_thumbnail_2", "image_old_thumbnail_3"
            ]
            
            # APPROACH 4: Try to download images from form fields
            if not downloaded_images:
                self.logger.info("Trying to download images from form fields")
                
                for field in image_fields:
                    if field in form_fields and form_fields[field]:
                        field_value = form_fields[field]
                        if not field_value:
                            continue
                        
                        # Construct image URL based on field value
                        img_url = f"{base_url}{field_value}"
                        filename = field_value
                        
                        try:
                            import requests
                            cookies = {cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()}
                            headers = {
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                                'Referer': self.driver.current_url
                            }
                            
                            response = requests.get(img_url, cookies=cookies, headers=headers, verify=False, timeout=10)
                            
                            if response.status_code == 200 and len(response.content) > 1000:
                                image_path = os.path.join(images_dir, filename)
                                with open(image_path, 'wb') as f:
                                    f.write(response.content)
                                
                                self.logger.info(f"Downloaded teacher image from form field {field}: {filename} (size: {len(response.content)} bytes)")
                                downloaded_images.append(filename)
                        except Exception as e:
                            self.logger.warning(f"Failed to download image for field {field}: {str(e)}")
            
            # Save image info
            image_info = {
                "downloaded_images": downloaded_images,
                "teacher_id": teacher_id,
                "image_fields": {field: form_fields.get(field, "") for field in image_fields if field in form_fields}
            }
            self._save_data_as_json(button_dir, "image_info.json", image_info)
            
            self.logger.info(f"Downloaded {len(downloaded_images)} teacher images")
            
            return downloaded_images
        except Exception as e:
            self._log_error("Failed to download teacher images", e)
            return []
    
    def _extract_bank_information(self, page_type=None):
        """Extract bank information from the 詳細 (Details) page"""
        bank_info = {}
        
        try:
            self.logger.info("Extracting bank information from 詳細 page")
            
            # First, try to extract bank information from the bank table (table_3 in tables_data.json)
            # This table is typically at the bottom of the 詳細 page
            bank_table_selectors = [
                "//table[contains(@class, 'IchiranTbl2') and .//th[contains(text(), '金融機関')]]",
                "//table[.//th[contains(text(), '金融機関')]]",
                "//table[.//td[contains(text(), '金融機関')]]",
                "//table[contains(@class, 'newsTbl') and .//th[contains(text(), '金融機関')]]"
            ]
            
            bank_table = None
            for selector in bank_table_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        bank_table = elements[0]
                        self.logger.info(f"Found bank information table using selector: {selector}")
                        break
                except Exception as e:
                    if self.debug_mode:
                        self.logger.debug(f"Error finding bank table with selector {selector}: {str(e)}")
            
            if bank_table:
                # Extract bank information from the table
                try:
                    # Get all input elements within the bank table
                    # This is more reliable than getting text from cells, as the values are in input fields
                    input_elements = bank_table.find_elements(By.CSS_SELECTOR, "input, select")
                    
                    # Process each input element
                    for input_elem in input_elements:
                        try:
                            # Get the input name and value
                            input_name = input_elem.get_attribute("name")
                            input_value = input_elem.get_attribute("value")
                            
                            if not input_name or not input_value:
                                continue
                            
                            # Map input names to standardized field names
                            if "ginko" in input_name or "bank_name" in input_name:
                                bank_info["bank_name"] = input_value
                            elif "ginko_code" in input_name or "bank_code" in input_name:
                                bank_info["bank_code"] = input_value
                            elif "shiten" in input_name and "code" not in input_name:
                                bank_info["bank_shiten"] = input_value
                            elif "shiten_code" in input_name:
                                bank_info["bank_shiten_code"] = input_value
                            elif "kouza_kind" in input_name or "bank_kouza_kind" in input_name:
                                bank_info["bank_kouza_kind"] = input_value
                            elif "kouza_number" in input_name or "bank_kouza_number" in input_name:
                                bank_info["bank_kouza_number"] = input_value
                            elif "kouza_kana" in input_name or "bank_kouza_kana" in input_name:
                                bank_info["bank_kouza_kana"] = input_value
                            elif "furikomi" in input_name:
                                bank_info["furikomi_umu"] = input_value
                        except Exception as input_error:
                            if self.debug_mode:
                                self.logger.debug(f"Error processing input element: {str(input_error)}")
                    
                    # If we couldn't get values from input elements, try to get them from the table cells
                    if not bank_info:
                        # Extract all rows from the table
                        rows = bank_table.find_elements(By.TAG_NAME, "tr")
                        
                        # Process each row to extract bank information
                        for row in rows:
                            try:
                                # Get cells (both th and td)
                                cells = row.find_elements(By.TAG_NAME, "th") + row.find_elements(By.TAG_NAME, "td")
                                
                                if len(cells) >= 2:
                                    # First cell is usually the label, second cell is the value
                                    label = cells[0].text.strip()
                                    
                                    # For the value, try to get it from an input field first
                                    input_elems = cells[1].find_elements(By.TAG_NAME, "input")
                                    if input_elems:
                                        value = input_elems[0].get_attribute("value")
                                    else:
                                        value = cells[1].text.strip()
                                    
                                    # Map common bank field labels to standardized field names
                                    if "金融機関名" in label:
                                        bank_info["bank_name"] = value
                                    elif "金融機関コード" in label:
                                        bank_info["bank_code"] = value
                                    elif "支店名" in label:
                                        bank_info["bank_shiten"] = value
                                    elif "支店コード" in label:
                                        bank_info["bank_shiten_code"] = value
                                    elif "口座種別" in label:
                                        bank_info["bank_kouza_kind"] = "普通" if "普通" in value else "当座" if "当座" in value else value
                                    elif "口座番号" in label:
                                        bank_info["bank_kouza_number"] = value
                                    elif "口座名義人" in label:
                                        bank_info["bank_kouza_kana"] = value
                                    elif "振込手数料" in label:
                                        bank_info["furikomi_umu"] = "有り" if "有り" in value else "無し" if "無し" in value else value
                            except Exception as row_error:
                                if self.debug_mode:
                                    self.logger.debug(f"Error processing bank table row: {str(row_error)}")
                except Exception as table_error:
                    self.logger.warning(f"Error extracting data from bank table: {str(table_error)}")
            
            # If we still don't have bank information, try a more direct approach
            if not bank_info or len(bank_info) < 3:  # If we have less than 3 bank fields, try other methods
                self.logger.info("Trying direct approach to extract bank information")
                
                # Try to find input fields directly by their IDs or names
                bank_field_mapping = {
                    "bank_name": ["bank_name", "ginko", "bank", "金融機関名", "ginko_name"],
                    "bank_code": ["bank_code", "ginko_code", "金融機関コード", "ginkocode"],
                    "bank_shiten": ["bank_shiten", "shiten", "支店名", "shiten_name"],
                    "bank_shiten_code": ["bank_shiten_code", "shiten_code", "支店コード", "shitencode"],
                    "bank_kouza_kind": ["bank_kouza_kind", "kouza_kind", "口座種別", "kouzakind"],
                    "bank_kouza_number": ["bank_kouza_number", "kouza_number", "口座番号", "kouzano"],
                    "bank_kouza_kana": ["bank_kouza_kana", "kouza_kana", "口座名義人", "kouzakana"]
                }
                
                for field_name, possible_ids in bank_field_mapping.items():
                    for field_id in possible_ids:
                        try:
                            # Try different selectors for each possible field ID
                            selectors = [
                                f"input[name='{field_id}']",
                                f"input[id='{field_id}']",
                                f"select[name='{field_id}']",
                                f"select[id='{field_id}']",
                                f"input[name*='{field_id}']",
                                f"input[id*='{field_id}']",
                                f"select[name*='{field_id}']",
                                f"select[id*='{field_id}']"
                            ]
                            
                            for selector in selectors:
                                try:
                                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                                    if elements:
                                        value = elements[0].get_attribute("value")
                                        if value:
                                            bank_info[field_name] = value
                                            break
                                except:
                                    pass
                        except Exception as field_error:
                            if self.debug_mode:
                                self.logger.debug(f"Error finding bank field {field_id}: {str(field_error)}")
            
            # Try to extract bank information from the page source as a last resort
            if not bank_info or len(bank_info) < 3:
                self.logger.info("Trying to extract bank information from page source")
                
                # Get the page source
                page_source = self.driver.page_source
                
                # Use regular expressions to find bank information patterns
                import re
                
                # Common patterns for bank information in Japanese systems
                patterns = {
                    "bank_name": r'金融機関名[：:]*\s*([^\s<>]+)',
                    "bank_code": r'金融機関コード[：:]*\s*(\d+)',
                    "bank_shiten": r'支店名[：:]*\s*([^\s<>]+)',
                    "bank_shiten_code": r'支店コード[：:]*\s*(\d+)',
                    "bank_kouza_number": r'口座番号[：:]*\s*(\d+)',
                    "bank_kouza_kana": r'口座名義人[：:]*\s*([^\s<>]+)'
                }
                
                for field, pattern in patterns.items():
                    if field not in bank_info or not bank_info[field]:
                        match = re.search(pattern, page_source)
                        if match:
                            bank_info[field] = match.group(1)
            
            # Set default values for missing fields
            if "bank_kouza_kind" not in bank_info or not bank_info["bank_kouza_kind"]:
                bank_info["bank_kouza_kind"] = "1"  # Default to "普通" (ordinary account)
            
            # Log the extracted bank information
            if bank_info:
                self.logger.info(f"Extracted bank information: {bank_info}")
            else:
                self.logger.warning("Could not extract any bank information")
            
            return bank_info
            
        except Exception as e:
            self._log_error("Failed to extract bank information", e)
            return {}
    
    def _extract_form_fields(self, page_type=None):
        """Extract form fields from the current page with enhanced detection and caching"""
        # Check if we have cached structure for this page type
        global PAGE_STRUCTURE_CACHE
        if page_type and page_type in PAGE_STRUCTURE_CACHE.get("form_field_selectors", {}):
            self.logger.info(f"Using cached form field selectors for {page_type}")
            selectors = PAGE_STRUCTURE_CACHE["form_field_selectors"][page_type]
        else:
            # Default selectors
            selectors = [
                "input[type='text']", 
                "input[type='hidden']",
                "input[type='radio']:checked", 
                "input[type='checkbox']:checked",
                "select", 
                "textarea"
            ]
            # Cache the selectors if page_type is provided
            if page_type:
                if "form_field_selectors" not in PAGE_STRUCTURE_CACHE:
                    PAGE_STRUCTURE_CACHE["form_field_selectors"] = {}
                PAGE_STRUCTURE_CACHE["form_field_selectors"][page_type] = selectors
        
        form_fields = {}
        
        try:
            # Extract form fields using the selectors
            for selector in selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if self.debug_mode:
                    self.logger.debug(f"Found {len(elements)} elements with selector: {selector}")
                
                for element in elements:
                    try:
                        # Get element ID, name, or other identifier
                        elem_id = element.get_attribute("id") or element.get_attribute("name")
                        
                        if not elem_id:
                            # Try to generate an identifier based on element attributes
                            elem_class = element.get_attribute("class")
                            elem_type = element.get_attribute("type")
                            if elem_class:
                                elem_id = f"{elem_type}_{elem_class}"
                            else:
                                # Skip elements without any identifier
                                continue
                        
                        # Get element value
                        if element.tag_name.lower() == "select":
                            # For select elements, get the selected option text
                            try:
                                selected_option = element.find_element(By.CSS_SELECTOR, "option:checked")
                                value = selected_option.text
                                
                                # Store the selected value, not all options
                                form_fields[elem_id] = value
                                
                                # Log the field for analysis in debug mode
                                if self.debug_mode:
                                    self.logger.debug(f"Found select field: {elem_id} = {value}")
                                
                                # Skip adding this to form_fields again
                                continue
                            except:
                                value = element.get_attribute("value")
                        else:
                            # For other input elements, get the value
                            value = element.get_attribute("value")
                        
                        # Store in form fields
                        form_fields[elem_id] = value
                        
                        # Log the field for analysis in debug mode
                        if self.debug_mode:
                            self.logger.debug(f"Found form field: {elem_id} = {value}")
                    except Exception as field_error:
                        if self.debug_mode:
                            self.logger.warning(f"Error extracting field: {field_error}")
                        continue
            
            # Also look for label/value pairs that might not be in form fields
            if self.debug_mode or not page_type or page_type not in PAGE_STRUCTURE_CACHE.get("label_fields", {}):
                try:
                    # Find all label elements
                    labels = self.driver.find_elements(By.TAG_NAME, "label")
                    if self.debug_mode:
                        self.logger.debug(f"Found {len(labels)} label elements")
                    
                    for label in labels:
                        try:
                            label_text = label.text.strip()
                            if not label_text:
                                continue
                            
                            # Try to find the associated input
                            label_for = label.get_attribute("for")
                            if label_for:
                                try:
                                    input_elem = self.driver.find_element(By.ID, label_for)
                                    input_value = input_elem.get_attribute("value")
                                    form_fields[f"label_{label_for}"] = label_text
                                    form_fields[label_for] = input_value
                                    if self.debug_mode:
                                        self.logger.debug(f"Found label-input pair: {label_text} = {input_value}")
                                except:
                                    # If we can't find the input, just store the label text
                                    form_fields[f"label_{label_for}"] = label_text
                        except:
                            continue
                except Exception as label_error:
                    if self.debug_mode:
                        self.logger.warning(f"Error processing labels: {label_error}")
                
                # Cache label fields if page_type is provided
                if page_type:
                    if "label_fields" not in PAGE_STRUCTURE_CACHE:
                        PAGE_STRUCTURE_CACHE["label_fields"] = {}
                    PAGE_STRUCTURE_CACHE["label_fields"][page_type] = True
            
            # Look for div/span elements that might contain field labels and values
            if self.debug_mode or not page_type or page_type not in PAGE_STRUCTURE_CACHE.get("container_fields", {}):
                try:
                    # Common patterns for field containers
                    field_containers = self.driver.find_elements(
                        By.CSS_SELECTOR, 
                        ".form-group, .field-container, .row, div.col, tr"
                    )
                    
                    for container in field_containers:
                        try:
                            # Look for label-like and value-like elements
                            label_elements = container.find_elements(
                                By.CSS_SELECTOR, 
                                "label, .label, strong, th, dt"
                            )
                            value_elements = container.find_elements(
                                By.CSS_SELECTOR, 
                                "input, select, textarea, .value, td, dd, span:not(.label)"
                            )
                            
                            if label_elements and value_elements:
                                label_text = label_elements[0].text.strip()
                                
                                # Get value from appropriate element
                                if value_elements[0].tag_name in ["input", "select", "textarea"]:
                                    value_text = value_elements[0].get_attribute("value")
                                else:
                                    value_text = value_elements[0].text.strip()
                                
                                if label_text and value_text:
                                    # Create a sanitized key from the label text
                                    key = label_text.lower().replace(" ", "_").replace(":", "")
                                    form_fields[f"container_{key}"] = value_text
                                    if self.debug_mode:
                                        self.logger.debug(f"Found container field: {label_text} = {value_text}")
                        except:
                            continue
                except Exception as container_error:
                    if self.debug_mode:
                        self.logger.warning(f"Error processing field containers: {container_error}")
                
                # Cache container fields if page_type is provided
                if page_type:
                    if "container_fields" not in PAGE_STRUCTURE_CACHE:
                        PAGE_STRUCTURE_CACHE["container_fields"] = {}
                    PAGE_STRUCTURE_CACHE["container_fields"][page_type] = True
            
            self.logger.info(f"Extracted {len(form_fields)} form fields in total")
            
        except Exception as e:
            self._log_error("Failed to extract form fields", e)
        
        return form_fields
    
    def _extract_tables(self, page_type=None):
        """Extract tables from the current page with enhanced detection and caching"""
        # Check if we have cached structure for this page type
        global PAGE_STRUCTURE_CACHE
        if page_type and page_type in PAGE_STRUCTURE_CACHE.get("table_selectors", {}):
            self.logger.info(f"Using cached table selectors for {page_type}")
            table_selectors = PAGE_STRUCTURE_CACHE["table_selectors"][page_type]
            div_table_selectors = PAGE_STRUCTURE_CACHE["div_table_selectors"][page_type]
        else:
            # Default selectors
            table_selectors = "table"
            div_table_selectors = ".table, .grid, .data-grid, div[role='grid'], div[role='table']"
            
            # Cache the selectors if page_type is provided
            if page_type:
                if "table_selectors" not in PAGE_STRUCTURE_CACHE:
                    PAGE_STRUCTURE_CACHE["table_selectors"] = {}
                if "div_table_selectors" not in PAGE_STRUCTURE_CACHE:
                    PAGE_STRUCTURE_CACHE["div_table_selectors"] = {}
                PAGE_STRUCTURE_CACHE["table_selectors"][page_type] = table_selectors
                PAGE_STRUCTURE_CACHE["div_table_selectors"][page_type] = div_table_selectors
        
        tables = []
        table_metadata = []
        
        try:
            # Look for all tables
            table_elements = self.driver.find_elements(By.TAG_NAME, table_selectors)
            if self.debug_mode:
                self.logger.debug(f"Found {len(table_elements)} table elements")
            
            for i, table_element in enumerate(table_elements):
                try:
                    # Get table attributes for metadata
                    table_id = table_element.get_attribute("id") or f"table_{i+1}"
                    table_class = table_element.get_attribute("class") or ""
                    
                    # Extract table data
                    table_data = []
                    rows = table_element.find_elements(By.TAG_NAME, "tr")
                    
                    for row in rows:
                        # Get cells (both th and td)
                        cells = row.find_elements(By.TAG_NAME, "th") + row.find_elements(By.TAG_NAME, "td")
                        row_data = [cell.text.strip() for cell in cells]
                        
                        if any(cell for cell in row_data):  # Skip empty rows
                            table_data.append(row_data)
                    
                    if table_data:  # Skip empty tables
                        tables.append(table_data)
                        
                        # Store metadata about this table
                        metadata = {
                            "id": table_id,
                            "class": table_class,
                            "rows": len(table_data),
                            "columns": len(table_data[0]) if table_data else 0,
                            "has_header": bool(table_element.find_elements(By.TAG_NAME, "th")),
                            "first_row": table_data[0] if table_data else []
                        }
                        table_metadata.append(metadata)
                        
                        if self.debug_mode:
                            self.logger.debug(f"Extracted table {table_id} with {len(table_data)} rows")
                except Exception as table_error:
                    if self.debug_mode:
                        self.logger.warning(f"Error extracting table {i}: {table_error}")
                    continue
            
            # Also look for div elements that might be structured like tables
            if self.debug_mode or not page_type or page_type not in PAGE_STRUCTURE_CACHE.get("div_tables_extracted", {}):
                try:
                    # Look for common table-like div structures
                    div_tables = self.driver.find_elements(By.CSS_SELECTOR, div_table_selectors)
                    
                    for i, div_table in enumerate(div_tables):
                        try:
                            # Check if this div contains row-like elements
                            row_elements = div_table.find_elements(
                                By.CSS_SELECTOR, 
                                ".row, .tr, div[role='row'], li"
                            )
                            
                            if row_elements:
                                table_data = []
                                
                                for row in row_elements:
                                    # Look for cell-like elements
                                    cell_elements = row.find_elements(
                                        By.CSS_SELECTOR, 
                                        ".cell, .td, .col, div[role='cell'], div[role='gridcell'], span"
                                    )
                                    
                                    if cell_elements:
                                        row_data = [cell.text.strip() for cell in cell_elements]
                                        if any(cell for cell in row_data):
                                            table_data.append(row_data)
                                
                                if table_data:
                                    tables.append(table_data)
                                    
                                    # Store metadata
                                    metadata = {
                                        "id": f"div_table_{i+1}",
                                        "class": div_table.get_attribute("class") or "",
                                        "rows": len(table_data),
                                        "columns": len(table_data[0]) if table_data else 0,
                                        "is_div_table": True,
                                        "first_row": table_data[0] if table_data else []
                                    }
                                    table_metadata.append(metadata)
                                    
                                    if self.debug_mode:
                                        self.logger.debug(f"Extracted div table with {len(table_data)} rows")
                        except Exception as div_error:
                            if self.debug_mode:
                                self.logger.warning(f"Error extracting div table {i}: {div_error}")
                            continue
                except Exception as div_tables_error:
                    if self.debug_mode:
                        self.logger.warning(f"Error processing div tables: {div_tables_error}")
                
                # Cache div tables extraction if page_type is provided
                if page_type:
                    if "div_tables_extracted" not in PAGE_STRUCTURE_CACHE:
                        PAGE_STRUCTURE_CACHE["div_tables_extracted"] = {}
                    PAGE_STRUCTURE_CACHE["div_tables_extracted"][page_type] = True
        
        except Exception as e:
            self._log_error("Failed to extract tables", e)
        
        return {"tables": tables, "metadata": table_metadata}
    
    def login(self):
        """Log into the system with enhanced reliability"""
        start_time = time.time()
        try:
            self.logger.info("Starting login process")
            
            # Initialize driver if not already done
            if not self.driver:
                if not self._initialize_driver():
                    return False
            
            # Navigate to login page
            self.driver.get(LOGIN_URL)
            self.logger.info(f"Navigated to login page: {LOGIN_URL}")
            
            # Save screenshot and page source for analysis in debug mode
            self._save_screenshot(self.output_dir, "login_page.png")
            self._save_page_source(self.output_dir, "login_page.html")
            
            # Try automated login with enhanced reliability
            try:
                # Wait for page to fully load
                time.sleep(1)
                
                # Simplified direct approach to login
                self.logger.info("Using direct login approach")
                
                # Take a screenshot of the login page for debugging
                if self.debug_mode:
                    self._save_screenshot(self.output_dir, "login_page_before_input.png")
                    self.logger.debug(f"Page title: {self.driver.title}")
                    self.logger.debug(f"Current URL: {self.driver.current_url}")
                
                # Use JavaScript to fill the login form directly
                login_script = f"""
                // Find the username field
                var usernameField = document.querySelector('input[name="login_id"]');
                if (usernameField) {{
                    usernameField.value = "{USERNAME}";
                    console.log("Set username to {USERNAME}");
                }} else {{
                    console.error("Username field not found");
                }}
                
                // Find the password field
                var passwordField = document.querySelector('input[name="login_pass"]');
                if (passwordField) {{
                    passwordField.value = "{PASSWORD}";
                    console.log("Set password");
                }} else {{
                    console.error("Password field not found");
                }}
                
                return {{
                    username: usernameField ? true : false,
                    password: passwordField ? true : false
                }};
                """
                
                # Execute the script
                form_results = self.driver.execute_script(login_script)
                self.logger.info(f"Form field detection results: {form_results}")
                
                # Take another screenshot after filling the form in debug mode
                if self.debug_mode:
                    self._save_screenshot(self.output_dir, "login_page_after_input.png")
                
                # Find login button with multiple selector options
                login_button = None
                selectors = [
                    (By.CSS_SELECTOR, "input[type='submit']"),
                    (By.XPATH, "//button[contains(text(), 'ログイン')]"),
                    (By.XPATH, "//input[@value='ログイン']"),
                    (By.CLASS_NAME, "btnLogin")
                ]
                
                for selector_type, selector in selectors:
                    try:
                        login_button = self.wait.until(EC.element_to_be_clickable((selector_type, selector)))
                        self.logger.info(f"Found login button using selector: {selector}")
                        break
                    except:
                        continue
                
                if not login_button:
                    raise Exception("Could not find login button with any selector")
                
                # Click login button
                login_button.click()
                self.logger.info("Clicked login button")
                
                # Wait for login to complete with more reliable check
                success_indicators = [
                    "//body[contains(., '本部用ツール')]",
                    "//a[contains(@href, 'logout.php')]",
                    "//div[contains(@class, 'header') and contains(., '本部')]"
                ]
                
                login_success = False
                for indicator in success_indicators:
                    try:
                        self.wait.until(EC.presence_of_element_located((By.XPATH, indicator)))
                        login_success = True
                        self.logger.info(f"Login verified with indicator: {indicator}")
                        break
                    except:
                        continue
                
                if not login_success:
                    raise Exception("Could not verify successful login with any indicator")
                
                self.logger.info("Login completed successfully")
                
                # Save screenshot and page source after login in debug mode
                self._save_screenshot(self.output_dir, "after_login.png")
                self._save_page_source(self.output_dir, "after_login.html")
                
                return True
            
            except Exception as e:
                self.logger.warning(f"Automated login failed: {str(e)}")
                self.logger.info("Falling back to manual login method")
                
                # Manual login approach - wait for redirect or timeout
                max_wait = 60  # seconds
                start_time = time.time()
                
                print("\n*** MANUAL LOGIN REQUIRED ***")
                print("Please log in manually in the browser window.")
                print(f"You have {max_wait} seconds to complete the login.")
                
                while time.time() - start_time < max_wait:
                    if "tool/" in self.driver.current_url:
                        self.logger.info("Manual login detected - user logged in successfully")
                        
                        # Save screenshot and page source after login in debug mode
                        self._save_screenshot(self.output_dir, "after_manual_login.png")
                        self._save_page_source(self.output_dir, "after_manual_login.html")
                        
                        return True
                    time.sleep(1)
                
                self._log_error("Login timeout - please log in manually within the time limit")
                return False
        
        except Exception as e:
            self._log_error("Login process failed", e)
            return False
        finally:
            self.timings["login"] = time.time() - start_time
    
    def navigate_to_teacher_list(self):
        """Navigate to the teacher list page"""
        start_time = time.time()
        try:
            self.logger.info(f"Navigating to teacher list page: {TEACHER_LIST_URL}")
            
            # Navigate to teacher list URL
            self.driver.get(TEACHER_LIST_URL)
            
            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Save screenshot and page source in debug mode
            self._save_screenshot(self.output_dir, "teacher_list_before_search.png")
            self._save_page_source(self.output_dir, "teacher_list_before_search.html")
            
            # Click search button to display teachers
            try:
                search_button = self.wait.until(EC.element_to_be_clickable(
                    (By.XPATH, "//input[@type='submit' and @name='search' and @value='検　索']")
                ))
                search_button.click()
                self.logger.info("Clicked search button to display teachers")
                time.sleep(2)
                
                # Save screenshot and page source after search in debug mode
                self._save_screenshot(self.output_dir, "teacher_list_after_search.png")
                self._save_page_source(self.output_dir, "teacher_list_after_search.html")
            except:
                self.logger.info("Could not find search button, checking if teacher table is already visible")
            
            # Wait for teacher table to be visible - try multiple selectors
            table_selectors = [
                "//table[contains(@class, 'list_tbl')]",
                "//table[contains(@class, 'newsTbl')]",
                "//table[contains(@class, 'IchiranTbl2')]",  # Added this selector based on the actual table class
                "//table[contains(@summary, 'お知らせ一覧')]"  # Added this selector based on the table summary
            ]
            
            teacher_table = None
            for selector in table_selectors:
                try:
                    teacher_table = self.wait.until(EC.presence_of_element_located((By.XPATH, selector)))
                    self.logger.info(f"Found teacher table using selector: {selector}")
                    break
                except:
                    continue
            
            if not teacher_table:
                self._log_error("Could not find teacher list table")
                return False
            
            # Save screenshot and page source after finding the table in debug mode
            self._save_screenshot(self.output_dir, "teacher_list_table_found.png")
            self._save_page_source(self.output_dir, "teacher_list_table_found.html")
            
            # Extract pagination information if available
            try:
                # Look for pagination info text
                page_info_text = self.driver.find_element(By.XPATH, "//td[contains(text(), '件中') and contains(text(), '件')]").text
                if page_info_text:
                    self.logger.info(f"Found pagination info: {page_info_text}")
                    
                    # Extract current page and total records
                    import re
                    match = re.search(r'(\d+)件中(\d+)～(\d+)件', page_info_text)
                    if match:
                        total_records = int(match.group(1))
                        start_record = int(match.group(2))
                        end_record = int(match.group(3))
                        
                        self.logger.info(f"Pagination details: Total records: {total_records}, Current page: {start_record}-{end_record}")
            except:
                self.logger.info("Could not find pagination information")
            
            return True
            
        except Exception as e:
            self._log_error("Failed to navigate to teacher list", e)
            return False
        finally:
            self.timings["navigate_to_teacher_list"] = time.time() - start_time
    
    def find_teacher(self):
        """Find the teacher to extract based on the teacher_index"""
        start_time = time.time()
        try:
            self.logger.info(f"Finding teacher at index {self.teacher_index}")
            
            # Find all teacher rows
            teacher_rows = None
            
            # Try different selectors for teacher rows
            row_selectors = [
                "//table[contains(@class, 'list_tbl')]//tr[position() > 1]",
                "//table[contains(@class, 'newsTbl')]//tr[position() > 1]",
                "//table[contains(@class, 'IchiranTbl2')]//tr[position() > 1]",
                "//table[contains(@summary, 'お知らせ一覧')]//tr[position() > 1]"
            ]
            
            for selector in row_selectors:
                try:
                    teacher_rows = self.driver.find_elements(By.XPATH, selector)
                    if teacher_rows:
                        self.logger.info(f"Found {len(teacher_rows)} teacher rows using selector: {selector}")
                        break
                except:
                    continue
            
            if not teacher_rows:
                self._log_error("Could not find any teacher rows")
                return False
            
            # Check if the requested index is valid
            if self.teacher_index >= len(teacher_rows):
                self._log_error(f"Teacher index {self.teacher_index} is out of range (0-{len(teacher_rows)-1})")
                return False
            
            # Get the teacher row at the specified index
            teacher_row = teacher_rows[self.teacher_index]
            
            # Extract basic teacher information from the row
            try:
                # Extract cells
                cells = teacher_row.find_elements(By.TAG_NAME, "td")
                
                if len(cells) >= 6:  # Ensure we have enough cells
                    teacher_id = cells[0].text.strip()
                    teacher_public = cells[1].text.strip()
                    teacher_type = cells[2].text.strip()
                    teacher_category = cells[3].text.strip()
                    teacher_name = cells[4].text.strip()
                    teacher_studio = cells[5].text.strip()
                    
                    # Store teacher info
                    self.teacher_info = {
                        "id": teacher_id,
                        "public": teacher_public,
                        "type": teacher_type,
                        "category": teacher_category,
                        "name": teacher_name,
                        "studio": teacher_studio
                    }
                    
                    # Update stats
                    self.stats["teacher_id"] = teacher_id
                    self.stats["teacher_name"] = teacher_name
                    
                    self.logger.info(f"Selected teacher: ID={teacher_id}, Name={teacher_name}")
                    
                    # Save teacher info
                    self._save_data_as_json(self.output_dir, "teacher_basic_info.json", self.teacher_info)
                    
                    return True
                else:
                    self._log_error(f"Teacher row has insufficient cells: {len(cells)}")
                    return False
            
            except Exception as e:
                self._log_error("Failed to extract teacher information from row", e)
                return False
        
        except Exception as e:
            self._log_error("Failed to find teacher", e)
            return False
        finally:
            self.timings["find_teacher"] = time.time() - start_time
    
    def process_teacher_buttons(self):
        """Process all buttons for the selected teacher"""
        start_time = time.time()
        try:
            teacher_id = self.stats.get('teacher_id')
            teacher_name = self.stats.get('teacher_name')
            self.logger.info(f"Processing buttons for teacher {teacher_name} (ID: {teacher_id})")
            
            # Find all teacher rows again
            teacher_rows = None
            
            # Try different selectors for teacher rows
            row_selectors = [
                "//table[contains(@class, 'list_tbl')]//tr[position() > 1]",
                "//table[contains(@class, 'newsTbl')]//tr[position() > 1]",
                "//table[contains(@class, 'IchiranTbl2')]//tr[position() > 1]",
                "//table[contains(@summary, 'お知らせ一覧')]//tr[position() > 1]"
            ]
            
            for selector in row_selectors:
                try:
                    teacher_rows = self.driver.find_elements(By.XPATH, selector)
                    if teacher_rows:
                        break
                except:
                    continue
            
            if not teacher_rows or self.teacher_index >= len(teacher_rows):
                self._log_error("Could not find teacher row again")
                return False
            
            # Get the teacher row
            teacher_row = teacher_rows[self.teacher_index]
            
            # Process each button type
            for button_type in self.button_types:
                button_start_time = time.time()
                
                try:
                    self.logger.info(f"Processing button: {button_type}")
                    
                    # Create directory for this button type directly in the output directory
                    button_dir = os.path.join(self.output_dir, button_type)
                    os.makedirs(button_dir, exist_ok=True)
                    
                    # Find the button in the row - use a more precise approach
                    button_found = False
                    
                    # First, try to find the button directly within the row
                    try:
                        # Look for input elements with the specific value within this row
                        buttons = teacher_row.find_elements(By.CSS_SELECTOR, f"input[value='{button_type}']")
                        if buttons:
                            button = buttons[0]
                            button_found = True
                            self.logger.info(f"Found {button_type} button using CSS selector within row")
                    except Exception as e:
                        self.logger.warning(f"Error finding button with CSS selector: {str(e)}")
                    
                    # If not found, try other approaches
                    if not button_found:
                        try:
                            # Look for any input elements in the row
                            inputs = teacher_row.find_elements(By.TAG_NAME, "input")
                            for input_elem in inputs:
                                value = input_elem.get_attribute("value")
                                if value == button_type:
                                    button = input_elem
                                    button_found = True
                                    self.logger.info(f"Found {button_type} button by checking input values")
                                    break
                        except Exception as e:
                            self.logger.warning(f"Error finding button by input values: {str(e)}")
                    
                    # If still not found, try a more general approach
                    if not button_found:
                        try:
                            # Look for any elements in the row that might be buttons
                            elements = teacher_row.find_elements(By.XPATH, ".//*")
                            for elem in elements:
                                # Check various attributes that might indicate this is our button
                                value = elem.get_attribute("value")
                                text = elem.text
                                tag = elem.tag_name
                                
                                if (value and value == button_type) or (text and text == button_type):
                                    button = elem
                                    button_found = True
                                    self.logger.info(f"Found {button_type} button using general element search")
                                    break
                        except Exception as e:
                            self.logger.warning(f"Error in general button search: {str(e)}")
                    
                    if not button_found:
                        self.logger.warning(f"Could not find {button_type} button")
                        continue
                    
                    # Click the button
                    try:
                        # Save screenshot before clicking
                        self._save_screenshot(button_dir, "before_click.png")
                        
                        # Get the onclick attribute to extract the JavaScript function
                        onclick = button.get_attribute("onclick")
                        self.logger.info(f"Button onclick attribute: {onclick}")
                        
                        # If we have an onclick attribute, try to extract the teacher ID and use direct navigation
                        if onclick and "teacher_id" in onclick:
                            # Extract the teacher ID from the onclick attribute
                            import re
                            teacher_id_match = re.search(r'teacher_id\.value=(\d+)', onclick)
                            if teacher_id_match:
                                extracted_id = teacher_id_match.group(1)
                                self.logger.info(f"Extracted teacher ID from onclick: {extracted_id}")
                                
                                # Verify that the extracted ID matches the expected teacher ID
                                if extracted_id != self.stats["teacher_id"]:
                                    self.logger.warning(f"Extracted teacher ID ({extracted_id}) doesn't match expected teacher ID ({self.stats['teacher_id']})")
                            
                            # Use the button click approach - this is what works in the staff crawler
                            self.logger.info(f"Using button click approach for {button_type}")
                            button.click()
                            self.logger.info(f"Clicked {button_type} button")
                        else:
                            # If we don't have an onclick attribute, just click the button
                            button.click()
                            self.logger.info(f"Clicked {button_type} button (no onclick attribute)")
                        
                        # Wait for page to load
                        time.sleep(2)
                        
                        # Save screenshot and page source after clicking
                        self._save_screenshot(button_dir, "after_click.png")
                        self._save_page_source(button_dir, "after_click.html")
                        
                        # Extract data from the page
                        form_fields = self._extract_form_fields(button_type)
                        tables_data = self._extract_tables(button_type)
                        
                        # If this is the 詳細 button, extract bank information and download teacher images
                        if button_type == "詳細":
                            # Extract bank information
                            bank_info = self._extract_bank_information(button_type)
                            # Add bank information to form fields
                            if bank_info:
                                form_fields.update(bank_info)
                                self.logger.info(f"Added {len(bank_info)} bank information fields to form fields")
                            
                            # Download teacher images
                            self._download_teacher_images(button_dir, form_fields)
                        
                        # Save extracted data
                        self._save_data_as_json(button_dir, "form_fields.json", form_fields)
                        self._save_data_as_json(button_dir, "tables_data.json", tables_data)
                        
                        # Update stats
                        self.stats["button_results"][button_type]["success"] = True
                        self.stats["button_results"][button_type]["fields_found"] = len(form_fields)
                        self.stats["button_results"][button_type]["tables_found"] = len(tables_data["tables"])
                        
                        # Navigate back to teacher list
                        self.driver.get(TEACHER_LIST_URL)
                        time.sleep(2)
                        
                        # Click search button again to display teachers
                        try:
                            search_button = self.wait.until(EC.element_to_be_clickable(
                                (By.XPATH, "//input[@type='submit' and @name='search' and @value='検　索']")
                            ))
                            search_button.click()
                            time.sleep(2)
                        except:
                            self.logger.info("Could not find search button when returning to list")
                        
                        # Find the teacher rows again
                        for selector in row_selectors:
                            try:
                                teacher_rows = self.driver.find_elements(By.XPATH, selector)
                                if teacher_rows and self.teacher_index < len(teacher_rows):
                                    teacher_row = teacher_rows[self.teacher_index]
                                    break
                            except:
                                continue
                        
                    except Exception as e:
                        self._log_error(f"Error processing {button_type} button", e)
                        
                        # Try to navigate back to teacher list
                        try:
                            self.driver.get(TEACHER_LIST_URL)
                            time.sleep(2)
                            
                            # Click search button again to display teachers
                            try:
                                search_button = self.wait.until(EC.element_to_be_clickable(
                                    (By.XPATH, "//input[@type='submit' and @name='search' and @value='検　索']")
                                ))
                                search_button.click()
                                time.sleep(2)
                            except:
                                pass
                            
                            # Find the teacher rows again
                            for selector in row_selectors:
                                try:
                                    teacher_rows = self.driver.find_elements(By.XPATH, selector)
                                    if teacher_rows and self.teacher_index < len(teacher_rows):
                                        teacher_row = teacher_rows[self.teacher_index]
                                        break
                                except:
                                    continue
                        except:
                            self._log_error("Failed to navigate back to teacher list after error")
                
                except Exception as e:
                    self._log_error(f"Failed to process {button_type} button", e)
                finally:
                    self.timings["button_timings"][button_type] = time.time() - button_start_time
            
            # Save final stats
            self._save_data_as_json(self.output_dir, "extraction_stats.json", self.stats)
            
            return True
        
        except Exception as e:
            self._log_error("Failed to process teacher buttons", e)
            return False
        finally:
            self.timings["process_teacher"] = time.time() - start_time
    
    def extract(self):
        """Main extraction process"""
        self.logger.info("Starting extraction process")
        
        try:
            # Login
            if not self.login():
                self._log_error("Login failed, cannot continue")
                return False
            
            # Navigate to teacher list
            if not self.navigate_to_teacher_list():
                self._log_error("Failed to navigate to teacher list, cannot continue")
                return False
            
            # Find teacher
            if not self.find_teacher():
                self._log_error("Failed to find teacher, cannot continue")
                return False
            
            # Process teacher buttons
            if not self.process_teacher_buttons():
                self._log_error("Failed to process teacher buttons")
                return False
            
            # Save final stats
            extraction_end = datetime.now()
            extraction_start = self.stats["extraction_start"]
            
            # Calculate duration
            if isinstance(extraction_start, str):
                try:
                    extraction_start = datetime.strptime(extraction_start, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    extraction_start = datetime.now()  # Fallback
            
            duration = extraction_end - extraction_start
            
            # Update stats
            self.stats["extraction_end"] = extraction_end
            self.stats["extraction_duration"] = str(duration)
            self.stats["timings"] = self.timings
            
            # Save stats
            self._save_data_as_json(self.output_dir, "extraction_stats.json", self.stats)
            
            self.logger.info("Extraction completed successfully")
            return True
        
        except Exception as e:
            self._log_error("Extraction process failed", e)
            return False
        finally:
            # Close WebDriver
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver closed")

def parse_arguments():
    """Parse command line arguments"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Extract data for a single teacher from EN Dance System Admin")
    parser.add_argument("-i", "--index", type=int, default=0, help="Index of the teacher to extract (0 = first teacher)")
    parser.add_argument("-o", "--output", type=str, help="Output directory (default: single_teacher_{timestamp})")
    parser.add_argument("--screenshots", action="store_true", help="Save screenshots during extraction")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode with more logging")
    
    return parser.parse_args()

def main():
    """Main function"""
    # Parse arguments
    args = parse_arguments()
    
    # Create extractor
    extractor = OptimizedTeacherExtractor(
        output_dir=args.output,
        teacher_index=args.index,
        save_screenshots=args.screenshots,
        debug_mode=args.debug
    )
    
    # Run extraction
    extractor.extract()

if __name__ == "__main__":
    main()
