# browser.py
import time
import traceback
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from config import HEADLESS, BROWSER_WIDTH, BROWSER_HEIGHT
from logger import logger

def init_driver(headless=HEADLESS):
    """
    Khởi tạo trình duyệt Chrome với các tùy chọn phù hợp
    """
    logger.info("Đang khởi tạo trình duyệt Chrome...")
    try:
        options = Options()
        if headless:
            options.add_argument('--headless=new')
        options.add_argument('--disable-gpu')
        options.add_argument(f'--window-size={BROWSER_WIDTH},{BROWSER_HEIGHT}')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-infobars')
        options.add_argument('--disable-notifications')
        options.add_argument('--lang=ja-JP')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36')
        
        # Sử dụng ChromeDriverManager để tự động tải và cài đặt ChromeDriver phù hợp
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        # Thiết lập kích thước cửa sổ
        driver.set_window_size(BROWSER_WIDTH, BROWSER_HEIGHT)
        logger.info("Khởi tạo trình duyệt Chrome thành công")
        return driver
    except Exception as e:
        logger.error(f"Lỗi khi khởi tạo trình duyệt Chrome: {e}")
        traceback.print_exc()
        sys.exit(1)
