# screenshot.py
import os
import base64
import time
import traceback
from logger import logger

def take_full_page_screenshot(driver, filename):
    """
    Ch<PERSON>p ảnh toàn bộ trang web sử dụng Chrome DevTools Protocol
    """
    logger.info(f"<PERSON><PERSON> chụp ảnh toàn trang web: {filename}")
    
    try:
        # <PERSON><PERSON><PERSON> bảo thư mục tồn tại
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        # L<PERSON>y kích thước trang sử dụng CDP
        page_metrics = driver.execute_cdp_cmd('Page.getLayoutMetrics', {})
        
        # C<PERSON>u hình thiết lập chụp ảnh
        screenshot_config = {
            'captureBeyondViewport': True,
            'fromSurface': True,
            'clip': {
                'width': page_metrics['contentSize']['width'],
                'height': page_metrics['contentSize']['height'],
                'x': 0,
                'y': 0,
                'scale': 1
            }
        }

        # Chụ<PERSON> ảnh sử dụng CDP
        result = driver.execute_cdp_cmd('Page.captureScreenshot', screenshot_config)
        
        # <PERSON><PERSON><PERSON> dữ liệu ảnh base64
        with open(filename, 'wb') as f:
            f.write(base64.b64decode(result['data']))
            
        logger.info(f"Đã lưu ảnh toàn trang thành công: {filename}")
        return True
        
    except Exception as e:
        logger.error(f"Lỗi khi chụp ảnh toàn trang với CDP: {e}")
        traceback.print_exc()
        
        # Phương pháp dự phòng nếu CDP không hoạt động
        try:
            logger.info("Thử phương pháp dự phòng để chụp ảnh...")
            # Lấy kích thước của trang web
            original_size = driver.get_window_size()
            required_width = driver.execute_script('return document.body.parentNode.scrollWidth')
            required_height = driver.execute_script('return document.body.parentNode.scrollHeight')
            
            # Đặt kích thước cửa sổ
            driver.set_window_size(required_width, required_height)
            time.sleep(2)  # Đợi để trang được render lại
            
            # Chụp ảnh
            driver.save_screenshot(filename)
            
            # Khôi phục kích thước cửa sổ
            driver.set_window_size(original_size['width'], original_size['height'])
            
            logger.info(f"Đã chụp ảnh toàn trang bằng phương pháp dự phòng: {filename}")
            return True
        except Exception as e2:
            logger.error(f"Lỗi khi chụp ảnh với phương pháp dự phòng: {e2}")
            traceback.print_exc()
            return False
