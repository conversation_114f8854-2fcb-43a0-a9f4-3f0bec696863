# EnDance Admin Crawler

This project contains scripts for crawling and extracting teacher information from the EnDance admin system (https://www.en-system.net/admin/).

## Features

- Automatic login to the admin system
- Navigation to the teacher list page
- Extraction of teacher information from the list page
- Detailed data extraction from teacher detail pages (詳細, 報酬設定, 署名)
- Image downloading for teacher profile images
- Support for extracting data from a single teacher or all teachers
- Pagination support for navigating through multiple pages of teachers
- Checkpoint and resume functionality for long-running extractions
- Detailed logging and statistics

## Scripts

### 1. extract_one_teacher.py

This script extracts detailed information for a single teacher.

```bash
python extract_one_teacher.py --index <teacher_index> [--debug] [--output <directory>]
```

Options:
- `--index`: Index of the teacher in the list (starting from 0)
- `--debug`: Enable debug mode with more detailed logging
- `--output`: Specify output directory (default: single_teacher_{timestamp})

### 2. extract_one_teacher_optimized.py

This is an optimized version of the single teacher extraction script with better error handling and performance.

```bash
python extract_one_teacher_optimized.py --index <teacher_index> [--debug] [--screenshots] [--output <directory>]
```

Options:
- `--index`: Index of the teacher in the list (starting from 0)
- `--debug`: Enable debug mode with more detailed logging
- `--screenshots`: Save screenshots during the extraction process
- `--output`: Specify output directory (default: single_teacher_{timestamp})

### 3. extract_all_teachers.py

This script extracts detailed information for all teachers, with support for pagination and resuming from checkpoints.

```bash
python extract_all_teachers.py [--output <directory>] [--max-pages <number>] [--start-page <number>] [--start-index <number>] [--time-limit <hours>] [--screenshots] [--debug] [--resume]
```

Options:
- `--output`: Output directory (default: all_teachers_{timestamp})
- `--max-pages`: Maximum number of pages to extract (default: all pages)
- `--start-page`: Page number to start extraction from (default: 1)
- `--start-index`: Index of the first teacher to extract on the start page (default: 0)
- `--time-limit`: Maximum runtime in hours (default: 3)
- `--screenshots`: Save screenshots during extraction
- `--debug`: Enable debug mode with more logging
- `--resume`: Resume from a previous extraction

### 4. config_teacher.py

Configuration file for teacher extraction, containing:
- Login credentials
- Teacher table selectors
- Pagination settings

### 5. config_buttons.py

Configuration file for button extraction, containing:
- Button types and selectors
- Form field selectors for each button type
- Data extraction configuration for each button type

## Data Structure

The extracted data is organized as follows:

### Single Teacher Extraction

```
single_teacher_{timestamp}/
├── teacher_basic_info.json
├── extraction_stats.json
├── 詳細/
│   ├── form_fields.json
│   ├── tables_data.json
│   └── images/
│       ├── {teacher_id}_1.jpg
│       ├── {teacher_id}_2.jpg
│       ├── {teacher_id}_3.jpg
│       └── {teacher_id}_thumbnail_1.jpg
├── 報酬設定/
│   ├── form_fields.json
│   └── tables_data.json
└── 署名/
    ├── form_fields.json
    └── tables_data.json
```

### All Teachers Extraction

```
all_teachers_{timestamp}/
├── extraction_stats.json
├── checkpoint.json
├── page_1/
│   └── teachers_info.json
├── page_2/
│   └── teachers_info.json
├── teacher_{id1}/
│   ├── teacher_info.json
│   ├── extraction_stats.json
│   ├── 詳細/
│   │   ├── form_fields.json
│   │   ├── tables_data.json
│   │   └── images/
│   │       ├── {teacher_id}_1.jpg
│   │       ├── {teacher_id}_2.jpg
│   │       ├── {teacher_id}_3.jpg
│   │       └── {teacher_id}_thumbnail_1.jpg
│   ├── 報酬設定/
│   │   ├── form_fields.json
│   │   └── tables_data.json
│   └── 署名/
│       ├── form_fields.json
│       └── tables_data.json
└── teacher_{id2}/
    └── ...
```

## Usage Examples

### Extract a single teacher

```bash
python extract_one_teacher.py --index 0 --debug
```

This will extract the first teacher in the list with detailed logging.

### Extract a single teacher with the optimized script

```bash
python extract_one_teacher_optimized.py --index 0 --debug --screenshots
```

This will extract the first teacher in the list with detailed logging and save screenshots.

### Extract all teachers

```bash
python extract_all_teachers.py --debug
```

This will extract all teachers with detailed logging.

### Extract a limited number of pages

```bash
python extract_all_teachers.py --max-pages 2 --debug
```

This will extract teachers from the first 2 pages.

### Resume a previous extraction

```bash
python extract_all_teachers.py --resume --debug
```

This will resume extraction from the last checkpoint.

## Backup

Use the backup script to create a backup of the current code:

```bash
.\admin_backup_latest_code.bat
```

This will create a backup of the current code in the `backup_src\admin_v{version}` directory.

## Implementation Details

### Button Handling

The scripts use a robust approach to find and click buttons on the teacher list page:

1. First, try to find buttons using verified selectors from `config_buttons.py`
2. If that fails, try multiple approaches:
   - Find by input value
   - Find by link text
   - Find by any element with the button text

### Image Downloading

For teacher images, the scripts use multiple approaches:

1. Browser-based approach: Open the image URL in a new tab and take a screenshot
2. Direct download with requests using browser cookies
3. Extract image URLs directly from the page

### Form Field Extraction

The scripts extract form fields using multiple selectors:

1. Input elements (text, hidden, radio, checkbox)
2. Select elements
3. Textarea elements
4. Label/value pairs
5. Div/span elements that might contain field labels and values

### Table Extraction

The scripts extract tables using multiple approaches:

1. Standard HTML tables
2. Div elements structured like tables
