@echo off
echo EN Dance System Teacher Information Extractor
echo ============================================

set /p username=Enter admin username: 
set /p password=Enter admin password: 

echo.
echo Select mode:
echo 1. Normal mode (visible browser)
echo 2. Headless mode (invisible browser)
echo 3. Debug mode (visible browser with debug logging)
echo.

set /p mode=Enter mode number (1-3): 

if "%mode%"=="1" (
    python extract_teacher.py --username %username% --password %password%
) else if "%mode%"=="2" (
    python extract_teacher.py --username %username% --password %password% --headless
) else if "%mode%"=="3" (
    python extract_teacher.py --username %username% --password %password% --debug
) else (
    echo Invalid mode selected. Please run again and select 1-3.
    pause
    exit /b 1
)

pause 