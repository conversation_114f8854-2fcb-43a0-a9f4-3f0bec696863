#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
EN Dance System Web Crawler

This script automates the extraction of data from the EN Dance System
for backup purposes. It logs in to the system, navigates through various pages,
and extracts data into CSV files.
"""

import os
import time
import logging
import argparse
import random
import psutil
import urllib3.exceptions
import selenium.common.exceptions
from datetime import datetime
from getpass import getpass

import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException, StaleElementReferenceException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

# Import configuration
try:
    import config
except ImportError:
    print("Configuration file not found. Please create config.py based on config_example.py.")
    exit(1)


# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("crawler.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ENDanceSystemCrawler:
    """
    A crawler for extracting data from the EN Dance System web management system.
    
    This crawler can:
    - Log in to the system with provided credentials
    - Navigate through different pages
    - Extract data from tables
    - Handle pagination
    - Save extracted data to CSV files
    """
    
    def __init__(self, username, password, headless=False, debug=False):
        """
        Initialize the crawler with login details and browser settings.
        
        Args:
            username (str): Username for login
            password (str): Password for login
            headless (bool): Whether to run Chrome in headless mode
            debug (bool): Whether to run in debug mode with pauses
        """
        # Set debug logging if debug mode is enabled
        if debug:
            logger.setLevel(logging.DEBUG)
            # Also set log level for root logger
            logging.getLogger().setLevel(logging.DEBUG)
            
        self.base_url = config.BASE_URL
        self.username = username
        self.password = password
        self.debug = debug
        self.session_start_time = time.time()
        self.max_session_duration = config.ADVANCED_OPTIONS.get("max_session_duration", 3600)  # Default 1 hour
        self.page_load_pause = config.ADVANCED_OPTIONS.get("page_load_pause", (1, 3))  # Random pause range
        self.human_like_delays = config.ADVANCED_OPTIONS.get("human_like_delays", True)
        self.max_cpu_percent = config.ADVANCED_OPTIONS.get("max_cpu_percent", 80)
        self.last_action_time = time.time()
        self.browser_crashes = 0
        self.max_browser_crashes = config.ADVANCED_OPTIONS.get("max_browser_crashes", 3)
        self.browser_healthy = True
        
        # Setup Chrome options
        self.chrome_options = Options()
        if headless or config.WEBDRIVER_CONFIG.get("headless", False):
            self.chrome_options.add_argument("--headless")
        
        self.chrome_options.add_argument(f"--window-size={config.WEBDRIVER_CONFIG.get('window_size', '1920,1080')}")
        
        if config.WEBDRIVER_CONFIG.get("disable_gpu", True):
            self.chrome_options.add_argument("--disable-gpu")
        
        if config.WEBDRIVER_CONFIG.get("disable_extensions", True):
            self.chrome_options.add_argument("--disable-extensions")
        
        if config.WEBDRIVER_CONFIG.get("no_sandbox", True):
            self.chrome_options.add_argument("--no-sandbox")
        
        if config.WEBDRIVER_CONFIG.get("disable_dev_shm_usage", True):
            self.chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Add language preference for Japanese
        self.chrome_options.add_argument("--lang=ja")
        
        # Initialize the driver
        self.initialize_driver()
    
    def initialize_driver(self):
        """Initialize or reinitialize the Chrome WebDriver"""
        try:
            # Close existing driver if it exists
            try:
                if hasattr(self, 'driver') and self.driver:
                    self.driver.quit()
            except:
                logger.warning("Failed to close existing WebDriver instance")
            
            # Create a new driver
            self.driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=self.chrome_options
            )
            self.wait = WebDriverWait(self.driver, config.WEBDRIVER_CONFIG.get("wait_timeout", 10))
            self.browser_healthy = True
            logger.info("WebDriver initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            self.browser_healthy = False
            return False
    
    def is_browser_connected(self):
        """Check if the browser is still connected and responsive"""
        try:
            # Simple check - get the current URL
            current_url = self.driver.current_url
            return True
        except (WebDriverException, urllib3.exceptions.MaxRetryError, 
                urllib3.exceptions.NewConnectionError, ConnectionRefusedError):
            logger.error("Browser connection lost")
            self.browser_healthy = False
            return False
        except Exception as e:
            logger.error(f"Error checking browser connection: {e}")
            self.browser_healthy = False
            return False
    
    def restart_browser_if_needed(self):
        """Restart the browser if it's not responsive"""
        if not self.browser_healthy or not self.is_browser_connected():
            logger.warning("Browser appears to be unhealthy or disconnected. Attempting to restart...")
            self.browser_crashes += 1
            
            if self.browser_crashes > self.max_browser_crashes:
                logger.error(f"Maximum browser crashes ({self.max_browser_crashes}) exceeded. Stopping crawler.")
                return False
            
            # Wait a moment before restarting
            time.sleep(2)
            
            # Kill any existing Chrome processes if we're having connection issues
            # This helps clear up stuck processes on Windows
            try:
                # Check if we're hitting connection refused errors specifically
                # which might indicate Chrome is still running but not responding
                import psutil
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        proc_name = proc.info['name'].lower()
                        if 'chrome' in proc_name or 'chromedriver' in proc_name:
                            logger.info(f"Terminating process: {proc.info['name']} (PID: {proc.info['pid']})")
                            proc.kill()
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass
                # Give processes time to terminate
                time.sleep(3)
            except Exception as e:
                logger.warning(f"Error while trying to kill Chrome processes: {e}")
            
            # Reinitialize the driver
            if not self.initialize_driver():
                logger.error("Failed to restart browser after crash")
                return False
            
            # Re-login after restart
            if not self.login():
                logger.error("Failed to login after browser restart")
                return False
            
            logger.info("Browser successfully restarted and logged in")
            return True
        
        return True
    
    def get_by_method(self, selector_dict):
        """Convert selector dictionary to Selenium By method."""
        by_method = selector_dict.get("by", "css").lower()
        if by_method == "id":
            return By.ID
        elif by_method == "name":
            return By.NAME
        elif by_method == "xpath":
            return By.XPATH
        elif by_method == "class":
            return By.CLASS_NAME
        elif by_method == "link_text":
            return By.LINK_TEXT
        elif by_method == "partial_link_text":
            return By.PARTIAL_LINK_TEXT
        elif by_method == "tag":
            return By.TAG_NAME
        else:
            return By.CSS_SELECTOR
    
    def human_like_delay(self, min_seconds=0.5, max_seconds=2.0):
        """Add a random delay to simulate human behavior."""
        if self.human_like_delays:
            delay = random.uniform(min_seconds, max_seconds)
            time.sleep(delay)
    
    def check_system_resources(self):
        """Check if system resources are overloaded and pause if needed."""
        cpu_percent = psutil.cpu_percent(interval=0.5)
        
        if cpu_percent > self.max_cpu_percent:
            logger.warning(f"High CPU usage detected: {cpu_percent}%. Pausing for system to recover...")
            recovery_time = random.uniform(3, 10)  # 3-10 seconds
            time.sleep(recovery_time)
            return False
        
        # Check if session has been running too long
        if time.time() - self.session_start_time > self.max_session_duration:
            logger.warning(f"Session has been running for {(time.time() - self.session_start_time)/60:.2f} minutes, which exceeds the maximum session duration.")
            if self.debug:
                user_input = input("Session duration limit reached. Continue anyway? (y/n): ")
                if user_input.lower() != 'y':
                    logger.info("Session terminated due to duration limit.")
                    return False
            else:
                logger.info("Session terminated due to duration limit.")
                return False
                
        return True
    
    def check_timeout(self):
        """Check if there's been no activity for too long."""
        idle_timeout = config.ADVANCED_OPTIONS.get("idle_timeout", 300)  # Default 5 minutes
        
        if time.time() - self.last_action_time > idle_timeout:
            logger.warning(f"No activity detected for {idle_timeout} seconds. Crawler may be stuck.")
            if self.debug:
                user_input = input("Crawler seems to be idle. Continue anyway? (y/n): ")
                if user_input.lower() != 'y':
                    logger.info("Session terminated due to inactivity.")
                    return False
            else:
                logger.info("Session terminated due to inactivity.")
                return False
        
        return True
    
    def update_activity_timestamp(self):
        """Update the last action timestamp."""
        self.last_action_time = time.time()
    
    def login(self):
        """
        Log in to the management system using the provided credentials.
        
        Returns:
            bool: True if login was successful, False otherwise
        """
        try:
            logger.info(f"Navigating to {self.base_url}")
            self.driver.get(self.base_url)
            
            # If manual login is enabled in config, prompt user to log in manually
            if self.debug or config.ADVANCED_OPTIONS.get("manual_login", False):
                logger.info("Manual login mode: Please log in manually in the browser.")
                input("Press Enter after you have logged in manually...")
                
                # Check if login was successful by looking for an element that exists after login
                try:
                    self.wait.until(
                        EC.presence_of_element_located((By.XPATH, "//body[contains(., '本部用ツール') or contains(., 'メインメニュー')]"))
                    )
                    logger.info("Manual login successful")
                    return True
                except TimeoutException:
                    logger.error("Login verification failed - couldn't find expected elements after login")
                    return False
            
            # Regular automated login process
            # Wait for login form to load and find input fields
            username_by = self.get_by_method(config.LOGIN_CONFIG["username_selector"])
            password_by = self.get_by_method(config.LOGIN_CONFIG["password_selector"])
            submit_by = self.get_by_method(config.LOGIN_CONFIG["submit_selector"])
            
            # Log HTML source for debugging
            if self.debug:
                logger.info(f"Login page HTML source: {self.driver.page_source[:500]}...")
            
            username_field = self.wait.until(
                EC.presence_of_element_located((username_by, config.LOGIN_CONFIG["username_selector"]["value"]))
            )
            password_field = self.driver.find_element(password_by, config.LOGIN_CONFIG["password_selector"]["value"])
            
            # Input credentials
            username_field.send_keys(self.username)
            password_field.send_keys(self.password)
            
            # Submit form
            submit_button = self.driver.find_element(submit_by, config.LOGIN_CONFIG["submit_selector"]["value"])
            submit_button.click()
            
            # Debug mode: pause for manual inspection
            if self.debug:
                logger.info("Debug mode: Pausing after login submission. Press Enter to continue...")
                input("Press Enter to continue after manually verifying login...")
                return True
            
            # Wait for login to complete
            success_by = self.get_by_method(config.LOGIN_CONFIG["success_indicator"])
            self.wait.until(
                EC.presence_of_element_located((success_by, config.LOGIN_CONFIG["success_indicator"]["value"]))
            )
            
            logger.info("Login successful")
            return True
            
        except (TimeoutException, NoSuchElementException) as e:
            logger.error(f"Login failed: {str(e)}")
            
            # Offer manual login as fallback
            if not config.ADVANCED_OPTIONS.get("manual_login", False):
                logger.info("Attempting manual login as fallback...")
                self.driver.get(self.base_url)
                logger.info("Please log in manually in the browser.")
                input("Press Enter after you have logged in manually...")
                
                # Verify login was successful
                try:
                    self.wait.until(
                        EC.presence_of_element_located((By.XPATH, "//body[contains(., '本部用ツール') or contains(., 'メインメニュー')]"))
                    )
                    logger.info("Manual login successful")
                    return True
                except TimeoutException:
                    logger.error("Login verification failed - couldn't find expected elements after login")
            
            if self.debug:
                logger.info("Debug mode: Browser will remain open. Manually check the login page.")
                input("Press Enter to continue after manually checking the browser...")
            return False
    
    def navigate_to_page(self, page_config):
        """
        Navigate to a specific page in the system with human-like behavior.
        """
        # Check system resources before proceeding
        if not self.check_system_resources() or not self.check_timeout() or not self.restart_browser_if_needed():
            return False
            
        url = page_config["url"]
        if not url.startswith("http"):
            url = f"{self.base_url}{url}"
            
        logger.info(f"Navigating to {url}")
        
        try:
            # Add human-like delay before navigation
            self.human_like_delay()
            
            # Navigate to the URL
            self.driver.get(url)
            
            # Update activity timestamp
            self.update_activity_timestamp()
            
            # Random delay after page load - like a human pausing to look at the page
            if self.human_like_delays:
                min_delay, max_delay = self.page_load_pause
                delay = random.uniform(min_delay, max_delay)
                logger.debug(f"Pausing for {delay:.1f} seconds after page load...")
                time.sleep(delay)
            
            # Wait for the title to be available
            for _ in range(3):  # Try a few times before giving up
                if self.driver.title:
                    break
                time.sleep(1)
            
            # Check for access error messages
            try:
                error_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'アクセスエラー') or contains(text(), 'Access Error') or contains(text(), '権限がありません')]")
                if error_elements:
                    error_text = error_elements[0].text
                    logger.warning(f"Access error detected on page {page_config['name']}: {error_text}")
                    
                    # Take a screenshot in debug mode
                    if self.debug and self.is_browser_connected():
                        try:
                            screenshot_path = f"error_page_{page_config['name'].replace(' ', '_')}_{int(time.time())}.png"
                            self.driver.save_screenshot(screenshot_path)
                            logger.debug(f"Saved screenshot of error page to {screenshot_path}")
                        except Exception as e:
                            logger.warning(f"Failed to take screenshot: {e}")
                    
                    # Mark this page to be skipped during this session
                    page_config['_skip_due_to_error'] = True
                    return False
            except Exception as e:
                logger.warning(f"Error checking for access errors: {e}")
                
            # In debug mode, pause for manual verification
            if self.debug:
                logger.info(f"Debug mode: Pausing after navigating to {url}. Press Enter to continue...")
                input("Press Enter to continue after manually verifying page navigation...")
            
            # IMMEDIATE CHECK: Check if this is a form page that requires input BEFORE doing anything else
            if page_config.get("requires_form_input", False):
                logger.info(f"This page ({page_config['name']}) is configured as a form page requiring user input")
                # Take a screenshot in debug mode
                if self.debug and self.is_browser_connected():
                    try:
                        screenshot_path = f"form_page_{page_config['name'].replace(' ', '_')}_{int(time.time())}.png"
                        self.driver.save_screenshot(screenshot_path)
                        logger.debug(f"Saved screenshot of form page to {screenshot_path}")
                    except Exception as e:
                        logger.warning(f"Failed to take screenshot: {e}")
                # Mark this page as needing form input for extract_table_data
                page_config['_form_detected'] = True
                
            # If not explicitly configured, still check for form elements that might indicate a form page
            else:
                try:
                    # Find form elements
                    forms = self.driver.find_elements(By.TAG_NAME, "form")
                    selects = self.driver.find_elements(By.TAG_NAME, "select")
                    inputs = self.driver.find_elements(By.TAG_NAME, "input")
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, "input[type='submit'], button[type='submit']")
                    
                    # Also check for any tables currently on the page
                    tables = self.driver.find_elements(By.CSS_SELECTOR, "table")
                    
                    # Quick check for form fields that typically require input
                    has_text_inputs = False
                    for input_elem in inputs:
                        try:
                            input_type = input_elem.get_attribute("type")
                            if input_type in ["text", "select", "date", "datetime", "checkbox", "radio"]:
                                has_text_inputs = True
                                break
                        except:
                            pass
                    
                    # Check if this appears to be a search/filter form that requires input
                    if forms and (((selects and len(selects) > 0) or has_text_inputs) and buttons and not tables):
                        logger.info(f"Auto-detected this page ({page_config['name']}) as a form that requires input")
                        
                        # Take a screenshot for debugging
                        if self.debug and self.is_browser_connected():
                            try:
                                screenshot_path = f"detected_form_{page_config['name'].replace(' ', '_')}_{int(time.time())}.png"
                                self.driver.save_screenshot(screenshot_path)
                                logger.info(f"Saved auto-detected form screenshot to {screenshot_path}")
                            except:
                                logger.warning("Failed to take screenshot")
                                
                        # Mark this page as needing form input for extract_table_data
                        page_config['_form_detected'] = True
                        logger.info(f"Consider updating config.py to mark '{page_config['name']}' as requires_form_input=True")
                except Exception as e:
                    logger.warning(f"Error checking for form elements: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error navigating to {url}: {e}")
            return False
    
    def extract_table_data(self, page_config):
        """
        Extract tabular data from a page with human-like behavior and proper error handling.
        """
        logger.info(f"Extracting table data from {page_config['name']}...")
        
        # Check system resources and browser health before proceeding
        if not self.check_system_resources() or not self.check_timeout() or not self.restart_browser_if_needed():
            return pd.DataFrame()
        
        # Skip if marked due to access error
        if page_config.get('_skip_due_to_error', False):
            logger.info(f"Skipping page {page_config['name']} due to previously detected access error")
            return pd.DataFrame()
        
        # IMMEDIATELY CHECK: If this page was detected as a form page during navigation
        if page_config.get('_form_detected', False) or page_config.get("requires_form_input", False):
            logger.info(f"Page {page_config['name']} requires form input before showing data")
            
            # In debug mode, ask if user wants to fill the form
            if self.debug and self.is_browser_connected():
                user_input = input(f"This page requires form input before showing data. Would you like to fill it manually? (y/n, default: n): ")
                if user_input.lower() == 'y':
                    logger.info("Waiting for user to fill the form manually...")
                    input("Fill the form and submit it, then press Enter when ready to continue...")
                    
                    # Now try to find table after form submission
                    try:
                        # Wait a moment for the page to load after form submission
                        time.sleep(3)
                        
                        # Check if any tables appeared after form submission
                        tables_after = self.driver.find_elements(By.CSS_SELECTOR, "table")
                        if tables_after:
                            logger.info(f"Table(s) found after form submission! Found {len(tables_after)} tables.")
                            # Continue with table extraction below
                        else:
                            logger.info("No tables found after form submission, skipping this page.")
                            return pd.DataFrame()
                    except Exception as e:
                        logger.error(f"Error after form submission: {e}")
                        return pd.DataFrame()
                else:
                    logger.info(f"Skipping form page: {page_config['name']}")
                    return pd.DataFrame()
            else:
                logger.info(f"Skipping form page that requires input: {page_config['name']}")
                return pd.DataFrame()
            
        # After form checks, proceed with table extraction
        table_selector = page_config.get('table_selector', 'table')
        retries = 0
        max_retries = config.ADVANCED_OPTIONS.get("max_retries", 5)
        
        # List of alternative selectors to try if the main one fails
        alternative_selectors = [
            'table',
            '.table',
            '.data-table',
            '.grid',
            '//table',
            '//div[contains(@class, "table")]',
            '//div[contains(@class, "grid")]',
            '.data-container table',
            '#main table',
            '.content table'
        ]
        
        while retries < max_retries:
            # Check if we should continue trying
            if not self.check_timeout() or not self.restart_browser_if_needed():
                return pd.DataFrame()
                
            try:
                # Add a human-like delay before retrying
                if retries > 0:
                    # Exponential backoff for retries - wait longer for each retry
                    backoff_time = min(2 ** retries, 10)  # Cap at 10 seconds
                    logger.info(f"Waiting {backoff_time} seconds before retry {retries+1}...")
                    time.sleep(backoff_time)
                
                # Update activity timestamp
                self.update_activity_timestamp()
                
                # First try to locate the table
                try:
                    if isinstance(table_selector, dict):
                        table = self.get_by_method(table_selector)
                    else:
                        # First try with CSS
                        try:
                            table = self.driver.find_element(By.CSS_SELECTOR, table_selector)
                        except (NoSuchElementException, WebDriverException):
                            # Then try with xpath if CSS fails
                            try:
                                table = self.driver.find_element(By.XPATH, table_selector)
                            except (NoSuchElementException, WebDriverException):
                                # If user-defined selector fails, try alternatives before giving up
                                table = None
                                for alt_selector in alternative_selectors:
                                    if not self.is_browser_connected():
                                        return pd.DataFrame()
                                        
                                    try:
                                        # Skip if it's the same as the one we already tried
                                        if alt_selector == table_selector:
                                            continue
                                            
                                        # Try CSS first
                                        if not alt_selector.startswith('//'):
                                            table = self.driver.find_element(By.CSS_SELECTOR, alt_selector)
                                        else:
                                            # Then XPath
                                            table = self.driver.find_element(By.XPATH, alt_selector)
                                        
                                        if table:
                                            logger.info(f"Found table using alternative selector: {alt_selector}")
                                            break
                                    except:
                                        continue
                                
                                # If still no table found, check if the page actually has a different structure
                                if not table:
                                    # First verify browser is still connected
                                    if not self.is_browser_connected():
                                        return pd.DataFrame()
                                    
                                    # Try to determine if this is a form page or non-tabular data
                                    try:
                                        forms = self.driver.find_elements(By.TAG_NAME, "form")
                                        inputs = self.driver.find_elements(By.TAG_NAME, "input")
                                        
                                        if forms and len(inputs) > 3:
                                            logger.info(f"Page appears to be a form page with {len(inputs)} input fields")
                                            if self.debug:
                                                user_input = input("This appears to be a form page, not a table. Skip this page? (y/n, default: y): ")
                                                if user_input.lower() != 'n':
                                                    logger.info(f"Skipping form page: {page_config['name']}")
                                                    return pd.DataFrame()
                                            else:
                                                logger.info(f"Skipping form page: {page_config['name']}")
                                                return pd.DataFrame()
                                    except WebDriverException:
                                        logger.error("Browser communication error while checking for forms")
                                        if not self.restart_browser_if_needed():
                                            return pd.DataFrame()
                                            
                                    # Take a screenshot for debugging
                                    if self.debug and self.is_browser_connected():
                                        try:
                                            screenshot_path = f"page_{page_config['name'].replace(' ', '_')}_{int(time.time())}.png"
                                            self.driver.save_screenshot(screenshot_path)
                                            logger.info(f"Saved screenshot to {screenshot_path}")
                                        except:
                                            logger.warning("Failed to take screenshot")
                                        
                                    # Check if there's any text content to save
                                    main_content = None
                                    if self.is_browser_connected():
                                        for selector in ['#main', '.content', 'main', 'body']:
                                            try:
                                                main_content = self.driver.find_element(By.CSS_SELECTOR, selector)
                                                break
                                            except:
                                                continue
                                                
                                        if main_content and main_content.text.strip():
                                            # If there's text content but no table, create a single-column DataFrame
                                            logger.info(f"No table found, but page has text content. Creating single-column data.")
                                            content_text = main_content.text.strip()
                                            # Split by lines and clean up
                                            lines = [line.strip() for line in content_text.split('\n') if line.strip()]
                                            df = pd.DataFrame({'Content': lines})
                                            return df
                                
                                    # If we've tried everything and found nothing, give up
                                    logger.warning(f"Could not find any table or meaningful content on page {page_config['name']}")
                                    if retries >= max_retries - 1:  # Last retry
                                        return pd.DataFrame()
                                    raise NoSuchElementException(f"Table not found using any selector")
                
                    # Table is found, proceed to extract data
                    if table and self.is_browser_connected():
                        # Rest of extraction logic
                        try:
                            # Try pandas HTML parsing first
                            tables = pd.read_html(table.get_attribute('outerHTML'))
                            if tables:
                                return tables[0]
                        except Exception as e:
                            logger.warning(f"Pandas HTML parsing failed: {e}")
                            
                        # Fallback to manual extraction
                        if not self.is_browser_connected():
                            return pd.DataFrame()
                            
                        try:
                            rows = table.find_elements(By.TAG_NAME, "tr")
                            if not rows:
                                raise ValueError("No rows found in table")
                            
                            headers = []
                            header_row = rows[0]
                            header_cells = header_row.find_elements(By.TAG_NAME, "th")
                            
                            # If no th elements, try td elements for header
                            if not header_cells:
                                header_cells = header_row.find_elements(By.TAG_NAME, "td")
                            
                            for cell in header_cells:
                                headers.append(cell.text.strip())
                            
                            # Generate default headers if none found
                            if not headers and len(rows) > 0:
                                # Try to determine column count from first data row
                                first_data_cells = rows[0].find_elements(By.TAG_NAME, "td")
                                if first_data_cells:
                                    headers = [f"Column{i+1}" for i in range(len(first_data_cells))]
                            
                            data = []
                            for row in rows:
                                cells = row.find_elements(By.TAG_NAME, "td")
                                if cells:
                                    row_data = []
                                    for cell in cells:
                                        row_data.append(cell.text.strip())
                                    data.append(row_data)
                            
                            # Create DataFrame
                            if data and headers:
                                # IMPROVED HANDLING FOR INCONSISTENT COLUMN COUNTS
                                # Find the maximum column count across all rows
                                max_cols = max([len(row) for row in data] + [len(headers)])
                                
                                # Ensure we have enough headers for all columns
                                if len(headers) < max_cols:
                                    headers.extend([f"Column{i+1}" for i in range(len(headers), max_cols)])
                                
                                # Pad data rows to match the max column count (fill with empty strings)
                                padded_data = []
                                for row in data:
                                    if len(row) < max_cols:
                                        padded_row = row + [''] * (max_cols - len(row))
                                        padded_data.append(padded_row)
                                    else:
                                        padded_data.append(row)
                                
                                # Create DataFrame with the padded data and headers
                                try:
                                    df = pd.DataFrame(padded_data, columns=headers[:max_cols])
                                    return df
                                except Exception as e:
                                    logger.error(f"Error creating DataFrame: {e}")
                                    
                                    # Last resort: create a DataFrame with default column names
                                    try:
                                        logger.warning("Attempting to create DataFrame with default column names...")
                                        df = pd.DataFrame(padded_data)
                                        return df
                                    except Exception as e2:
                                        logger.error(f"Failed to create DataFrame with default columns: {e2}")
                                        return pd.DataFrame()  # Return empty DataFrame as last resort
                            else:
                                logger.warning("No data or headers found in table")
                                return pd.DataFrame()
                        except WebDriverException as e:
                            logger.error(f"Browser communication error while extracting table: {e}")
                            if not self.restart_browser_if_needed():
                                return pd.DataFrame()
                        
                except NoSuchElementException as e:
                    logger.warning(f"Table not found on try {retries+1}: {e}")
                    retries += 1
                    
                    # If in debug mode, ask user what to do
                    if self.debug and self.is_browser_connected():
                        user_input = input(f"Table not found for {page_config['name']}. Press Enter to retry, 'skip' to skip this page, or enter a new table selector: ")
                        if user_input.lower() == 'skip':
                            logger.info(f"Skipping table extraction for {page_config['name']}")
                            return pd.DataFrame()  # Return empty DataFrame
                        elif user_input.strip():
                            # User provided a new selector
                            table_selector = user_input
                            retries = 0  # Reset retries with new selector
                            continue
                    
                    if retries >= max_retries:
                        logger.error(f"Failed to find table after {max_retries} attempts")
                        if self.debug and self.is_browser_connected():
                            # Take a screenshot for debugging
                            try:
                                screenshot_path = f"error_{page_config['name'].replace(' ', '_')}_{int(time.time())}.png"
                                self.driver.save_screenshot(screenshot_path)
                                logger.info(f"Saved screenshot to {screenshot_path}")
                            except:
                                logger.warning("Failed to take screenshot")
                        return pd.DataFrame()  # Return empty DataFrame instead of continuing to retry
                    
                    # Add progressive delay between retries - like a human would wait longer after each failure
                    time.sleep(retries * 1.5)  
                    
            except WebDriverException as e:
                logger.error(f"Browser error while extracting table data: {e}")
                if not self.restart_browser_if_needed():
                    return pd.DataFrame()
                retries += 1
                
            except Exception as e:
                logger.error(f"Error extracting table data: {e}")
                if self.debug and self.is_browser_connected():
                    input(f"Error: {e}. Press Enter to continue...")
                retries += 1
                if retries >= max_retries:
                    return pd.DataFrame()  # Return empty DataFrame on error after max retries
        
        # If we reach here, we've exceeded max retries
        logger.error(f"Failed to extract table after {max_retries} attempts")
        return pd.DataFrame()  # Return empty DataFrame
    
    def detect_menu_items(self):
        """
        Automatically detect all menu items from the left sidebar.
        This will extract the URLs and names of all accessible menu items after login.
        
        Returns:
            list: List of dictionaries with menu item information
        """
        try:
            logger.info("Detecting menu items from the left sidebar...")
            
            # Wait for the page to load after login
            self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//body[contains(., '本部用ツール')]"))
            )
            
            # Get the page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Find the sideLeft div to ensure we're looking at the right area
            side_left = soup.select_one('#sideLeft')
            if side_left:
                logger.debug(f"Found #sideLeft container: {side_left.name} with {len(side_left.contents)} children")
            else:
                logger.warning("Could not find #sideLeft container")
            
            # Find all SideNavi divs which contain the menu sections
            menu_containers = soup.select('div.SideNavi')
            
            if not menu_containers:
                logger.warning("No menu containers found with class 'SideNavi'")
                # Try more generic approach as fallback
                menu_containers = soup.select('#sideLeft div')
            
            logger.debug(f"Found {len(menu_containers)} menu containers")
            
            # Debug: Output the first menu container's HTML to see its structure
            if menu_containers and self.debug:
                first_container = menu_containers[0]
                logger.debug(f"First menu container HTML: {first_container}")
                # Check if the container has ul and li elements
                uls = first_container.select('ul')
                logger.debug(f"First container has {len(uls)} ul elements")
                if uls:
                    first_ul = uls[0]
                    lis = first_ul.select('li')
                    logger.debug(f"First ul has {len(lis)} li elements")
                    if lis:
                        first_li = lis[0]
                        logger.debug(f"First li HTML: {first_li}")
                        a_tags = first_li.select('a')
                        if a_tags:
                            logger.debug(f"First a tag: {a_tags[0]}")
                            logger.debug(f"First a tag href: {a_tags[0].get('href')}")
            
            detected_items = []
            section_titles = {}
            
            # Get section titles (h3 headings before each SideNavi div)
            h3_elements = soup.select('#sideLeft h3')
            for i, h3 in enumerate(h3_elements):
                if i < len(menu_containers):
                    section_titles[i] = h3.text.strip()
            
            # Process each menu container
            for i, container in enumerate(menu_containers):
                section_name = section_titles.get(i, f"Section {i+1}")
                logger.info(f"Processing menu section: {section_name}")
                
                # Find all links in this section
                links = container.select('li a')
                logger.info(f"Found {len(links)} links in section {section_name}")
                
                for link in links:
                    href = link.get('href')
                    # Debug: print every href found
                    logger.debug(f"Found link href: {href}")
                    
                    if href:
                        # Accept any valid URL, as we know these links are from the menu
                        # Default to treat all links as valid menu items
                        
                        # For absolute URLs with full domain
                        if href.startswith('http'):
                            # Extract just the filename from the full URL
                            if '/tool/' in href:
                                url = '/tool/' + href.split('/tool/')[-1]
                            else:
                                url = href
                        # For simple filenames like "group_main.php" - add /tool/ prefix
                        elif href.endswith('.php'):
                            url = '/tool/' + href
                        # For any other relative paths, ensure they have leading /
                        else:
                            url = href if href.startswith('/') else '/' + href
                        
                        # Get the text content of the link
                        name = link.get_text().strip()
                        
                        # Generate a filename based on the URL
                        filename = url.split('/')[-1].replace('.php', '').replace('.', '_') + '.csv'
                        
                        item = {
                            "name": name,
                            "url": url,
                            "filename": filename,
                            "table_selector": "table",  # Default selector
                            "pagination": {
                                "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
                            }
                        }
                        
                        detected_items.append(item)
                        logger.info(f"Detected menu item: {name} -> {url}")
            
            logger.info(f"Detected {len(detected_items)} menu items")
            
            # Save detected items to config file if requested
            if config.ADVANCED_OPTIONS.get("save_detected_menu", False):
                self.save_detected_menu_to_config(detected_items)
            
            return detected_items
            
        except Exception as e:
            logger.error(f"Error detecting menu items: {str(e)}")
            logger.error(f"Exception details: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            if self.debug:
                logger.info("Debug mode: Browser will remain open. Manually check the page.")
                input("Press Enter to continue after manually checking the browser...")
            return []
            
    def save_detected_menu_to_config(self, detected_items):
        """
        Save detected menu items to the config.py file for future use.
        
        Args:
            detected_items (list): List of dictionaries with menu item information
        """
        try:
            logger.info("Saving detected menu items to config file...")
            
            # Read current config.py file
            with open("config.py", "r", encoding="utf-8") as f:
                config_content = f.read()
            
            # Find the start and end of PAGES_TO_CRAWL section
            start_marker = "PAGES_TO_CRAWL = ["
            end_marker = "]"
            start_index = config_content.find(start_marker)
            
            if start_index == -1:
                logger.error("Could not find PAGES_TO_CRAWL section in config.py")
                return False
            
            start_index += len(start_marker)
            
            # Find the matching end bracket
            bracket_count = 1
            end_index = start_index
            while bracket_count > 0 and end_index < len(config_content):
                if config_content[end_index] == '[':
                    bracket_count += 1
                elif config_content[end_index] == ']':
                    bracket_count -= 1
                end_index += 1
            
            if bracket_count > 0:
                logger.error("Could not find end of PAGES_TO_CRAWL section in config.py")
                return False
            
            # Format detected items as Python code
            new_pages_content = "\n    # Auto-detected menu items - updated on " + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\n"
            
            # Get section names from the DOM
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            h3_elements = soup.select('#sideLeft h3')
            section_names = [h3.text.strip() for h3 in h3_elements]
            
            # Count links in each section to determine section boundaries
            section_boundaries = []
            running_count = 0
            
            menu_containers = soup.select('div.SideNavi')
            for container in menu_containers:
                links = container.select('li a')
                section_boundaries.append((running_count, running_count + len(links)))
                running_count += len(links)
            
            # Group items by section using the section boundaries
            items_by_section = {}
            for section_idx, (start, end) in enumerate(section_boundaries):
                if section_idx < len(section_names):
                    section_name = section_names[section_idx]
                    if start < len(detected_items):
                        items_in_section = detected_items[start:min(end, len(detected_items))]
                        items_by_section[section_name] = items_in_section
            
            # Add items by section
            for section_name, items in items_by_section.items():
                new_pages_content += f"\n    # {section_name}\n"
                for item in items:
                    new_pages_content += "    {\n"
                    new_pages_content += f'        "name": "{item["name"]}",  # Menu item name\n'
                    new_pages_content += f'        "url": "{item["url"]}",\n'
                    new_pages_content += f'        "filename": "{item["filename"]}",\n'
                    new_pages_content += f'        "table_selector": "{item["table_selector"]}",\n'
                    new_pages_content += '        "pagination": {\n'
                    new_pages_content += '            "next_button": {"by": "xpath", "value": "//a[contains(text(), \'次へ\')]"}\n'
                    new_pages_content += '        }\n'
                    new_pages_content += "    },\n"
            
            # Replace the old PAGES_TO_CRAWL content with the new one
            new_config_content = config_content[:start_index] + new_pages_content + config_content[end_index-1:]
            
            # Create a backup of the original config file
            backup_filename = f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            with open(backup_filename, "w", encoding="utf-8") as f:
                f.write(config_content)
            logger.info(f"Created backup of original config file: {backup_filename}")
            
            # Write the updated config file
            with open("config.py", "w", encoding="utf-8") as f:
                f.write(new_config_content)
            
            logger.info(f"Updated config.py with {len(detected_items)} detected menu items")
            return True
            
        except Exception as e:
            logger.error(f"Error saving detected menu to config: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    def run_backup(self, backup_dir=None):
        """Run a backup of all configured pages with stress management."""
        start_time = time.time()
        
        # Create backup directory with timestamp if not provided
        if not backup_dir:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_dir = f"backup_{timestamp}"
        
        # Create directory if it doesn't exist
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # Update OUTPUT_CONFIG with the new backup directory
        config.OUTPUT_CONFIG["output_dir"] = backup_dir
        logger.info(f"Backup directory: {backup_dir}")
        
        # Login to the system
        if not self.login():
            logger.error("Login failed. Cannot proceed with backup.")
            return False
        
        # Auto-detect menu items if enabled
        pages_to_crawl = config.PAGES_TO_CRAWL
        if config.ADVANCED_OPTIONS.get("auto_detect_menu", False):
            logger.info("Auto-detecting menu items...")
            detected_items = self.detect_menu_items()
            if detected_items:
                logger.info(f"Detected {len(detected_items)} menu items.")
                
                # Save detected menu to config if enabled
                if config.ADVANCED_OPTIONS.get("save_detected_menu", False):
                    logger.info("Saving detected menu items to config...")
                    self.save_detected_menu_to_config(detected_items)
                
                # Use detected items instead of configured ones
                pages_to_crawl = detected_items
        
        # Filter out pages marked to be skipped
        filtered_pages = []
        skipped_by_config = 0
        for page in pages_to_crawl:
            if page.get("skip", False):
                logger.info(f"Skipping page '{page['name']}' ({page['url']}) as marked in config")
                skipped_by_config += 1
            else:
                filtered_pages.append(page)
        
        if skipped_by_config > 0:
            logger.info(f"Skipped {skipped_by_config} pages marked in config")
            pages_to_crawl = filtered_pages
        
        # Backup each page
        total_pages = len(pages_to_crawl)
        successful_pages = 0
        total_tables = 0
        skipped_pages = 0
        
        logger.info(f"Starting backup of {total_pages} pages...")
        
        try:
            for i, page_config in enumerate(pages_to_crawl):
                # Check if we should continue based on system resources and browser health
                if not self.check_system_resources() or not self.restart_browser_if_needed():
                    logger.warning("System resources are constrained or browser health issues. Pausing backup...")
                    time.sleep(30)  # Wait 30 seconds for system to recover
                    if not self.check_system_resources() or not self.restart_browser_if_needed():
                        logger.error("System still constrained or browser issues persist after pause. Ending backup early.")
                        break
                
                # Check session duration
                elapsed_time = time.time() - start_time
                if elapsed_time > self.max_session_duration:
                    logger.warning(f"Session duration limit reached ({elapsed_time/60:.1f} minutes). Ending backup early.")
                    break
                
                logger.info(f"Processing page {i+1}/{total_pages}: {page_config['name']}")
                
                try:
                    # Add a human-like delay between pages - like a person clicking through
                    if i > 0:
                        delay = random.uniform(1.5, 4)
                        logger.debug(f"Pausing for {delay:.1f} seconds between pages...")
                        time.sleep(delay)
                    
                    # Navigate to the page first
                    if not self.navigate_to_page(page_config):
                        logger.error(f"Failed to navigate to {page_config['name']}. Skipping...")
                        skipped_pages += 1
                        continue
                    
                    # Extract table data
                    df = self.extract_table_data(page_config)
                    
                    # Check browser health after extraction
                    if not self.restart_browser_if_needed():
                        logger.error("Browser crashed during extraction. Skipping to next page.")
                        skipped_pages += 1
                        continue
                        
                    # If we got a valid DataFrame with data
                    if not df.empty:
                        # Save to CSV with proper encoding for Japanese characters
                        csv_path = os.path.join(
                            backup_dir,
                            page_config["filename"]
                        )
                        df.to_csv(
                            csv_path,
                            index=config.OUTPUT_CONFIG.get("include_index", False),
                            encoding=config.OUTPUT_CONFIG.get("csv_encoding", "utf-8-sig")
                        )
                        rows_count = len(df)
                        logger.info(f"Saved {rows_count} rows to {csv_path}")
                        total_tables += 1
                        successful_pages += 1
                    else:
                        logger.warning(f"No data extracted from {page_config['name']}")
                        skipped_pages += 1
                    
                    # Update activity timestamp
                    self.update_activity_timestamp()
                    
                    # Check if we're hitting timeouts or errors frequently
                    if skipped_pages > total_pages * 0.3:  # If more than 30% of pages are skipped
                        logger.warning(f"High skip rate detected ({skipped_pages}/{i+1}). System might be overloaded.")
                        if self.debug and self.is_browser_connected():
                            continue_backup = input("Many pages are being skipped. Continue backup? (y/n): ").lower()
                            if continue_backup != 'y':
                                logger.info("Backup terminated by user due to high skip rate.")
                                break
                
                except WebDriverException as e:
                    logger.error(f"Browser error while processing {page_config['name']}: {e}")
                    skipped_pages += 1
                    if not self.restart_browser_if_needed():
                        logger.error("Browser crashed and could not be restarted. Ending backup.")
                        break
                        
                except Exception as e:
                    logger.error(f"Error processing {page_config['name']}: {e}")
                    skipped_pages += 1
                    if self.debug and self.is_browser_connected():
                        input(f"Error processing {page_config['name']}. Press Enter to continue...")
        
        except KeyboardInterrupt:
            logger.info("Backup process interrupted by user (Ctrl+C). Shutting down gracefully...")
            # Fall through to closing section
            
        # Calculate and log statistics
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"Backup completed in {duration:.2f} seconds.")
        logger.info(f"Successfully backed up {successful_pages}/{total_pages} pages.")
        logger.info(f"Skipped {skipped_pages}/{total_pages} pages.")
        logger.info(f"Total tables saved: {total_tables}")
        logger.info(f"Browser crash/restart events: {self.browser_crashes}")
        
        # Close the browser if not in debug mode
        if not self.debug:
            logger.info("Closing browser...")
            try:
                self.driver.quit()
            except:
                logger.warning("Error while closing browser")
        else:
            logger.info("Debug mode: Browser left open. Remember to close the browser manually.")
        
        return successful_pages > 0  # Return True if at least one page was successful


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='EN Dance System Data Crawler')
    
    parser.add_argument('--headless', action='store_true', help='Run in headless mode (no browser UI)')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode with pauses for manual inspection')
    parser.add_argument('--output-dir', type=str, help='Directory to save extracted data')
    parser.add_argument('--username', type=str, help='Username for login')
    parser.add_argument('--password', type=str, help='Password for login (not recommended, use interactive prompt)')
    parser.add_argument('--auto-detect', action='store_true', help='Automatically detect menu items from the sidebar')
    parser.add_argument('--save-menu', action='store_true', help='Save detected menu items to config file')
    
    return parser.parse_args()


def main():
    """Main entry point for the crawler."""
    args = parse_arguments()
    
    # Get credentials - either from command line or prompt
    username = args.username or input("Enter your username: ")
    password = args.password or getpass("Enter your password: ")
    
    # Set auto-detect menu option if specified
    if args.auto_detect:
        config.ADVANCED_OPTIONS["auto_detect_menu"] = True
    
    # Set save menu option if specified
    if args.save_menu:
        config.ADVANCED_OPTIONS["save_detected_menu"] = True
    
    # Initialize crawler
    crawler = ENDanceSystemCrawler(
        username=username,
        password=password,
        headless=args.headless,
        debug=args.debug
    )
    
    try:
        # Run backup
        success = crawler.run_backup(args.output_dir)
        
        if success:
            logger.info("Crawler completed successfully")
            return 0
        else:
            logger.error("Crawler failed")
            return 1
    except KeyboardInterrupt:
        logger.info("Crawler stopped by user (Ctrl+C). Shutting down gracefully...")
        try:
            # Close browser gracefully
            if hasattr(crawler, 'driver') and crawler.driver:
                logger.info("Closing browser...")
                crawler.driver.quit()
        except Exception as e:
            logger.error(f"Error while closing browser: {e}")
        return 0
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        # Try to close browser
        try:
            if hasattr(crawler, 'driver') and crawler.driver:
                logger.info("Closing browser...")
                crawler.driver.quit()
        except:
            pass
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
