#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration file for the teacher buttons and navigation paths
"""

# Button configuration
BUTTON_CONFIG = {
    "詳細": {
        "selectors": [
            "//input[@value='詳細']",
        ],
        "back_selectors": [
            "//input[@type='button' and contains(@value, '戻る')]",
        ],
        "use_browser_back": False
    },
    "報酬設定": {
        "selectors": [
            "//input[@value='報酬設定']",
        ],
        "back_selectors": [
            "//input[@type='button' and contains(@value, '戻る')]",
        ],
        "use_browser_back": False
    },
    "署名": {
        "selectors": [
            "//input[@value='署名']",
        ],
        "back_selectors": [
            "//input[@type='button' and contains(@value, '戻る')]",
        ],
        "use_browser_back": False
    }
}

# Details about what data to extract from each page
DATA_EXTRACTION_CONFIG = {
    "詳細": {
        "tables": True,  # Extract table data
        "forms": True,   # Extract form field data
        "key_fields": [  # List of important field names or IDs to look for
            "name", "email", "phone", "address", "status", "category", "studio"
        ]
    },
    "報酬設定": {
        "tables": True,
        "forms": True,
        "key_fields": [
            "payment", "amount", "subscription", "payment_method", "salary_type"
        ]
    },
    "署名": {
        "tables": True,
        "forms": True,
        "key_fields": [
            "signature", "date", "document", "agreement"
        ]
    }
}

# This dictionary contains the verified configuration from testing
VERIFIED_BUTTON_CONFIG = {
    "詳細": {
        "selector": "//input[@value='詳細']",
        "back_selector": "//input[@type='button' and contains(@value, '戻る')]",
        "use_browser_back": False
    },
    "報酬設定": {
        "selector": "//input[@value='報酬設定']",
        "back_selector": "//input[@type='button' and contains(@value, '戻る')]",
        "use_browser_back": False
    },
    "署名": {
        "selector": "//input[@value='署名']",
        "back_selector": "//input[@type='button' and contains(@value, '戻る')]",
        "use_browser_back": False
    }
} 