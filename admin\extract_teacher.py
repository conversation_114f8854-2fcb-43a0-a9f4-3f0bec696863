#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
EN Dance System Teacher Information Extractor

This script automates the extraction of teacher information from the EN Dance System
admin interface. It can:
- Log in to the system automatically
- Navigate to the teacher list page
- Extract teacher information from the list
- Handle pagination
- Extract detailed information from each teacher's profile
"""

import os
import time
import logging
import argparse
import json
from datetime import datetime
from getpass import getpass

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# Import configurations
import config
import config_teacher_buttons as button_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("teacher_extraction.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TeacherExtractor:
    def __init__(self, username, password, headless=False, debug=False):
        """
        Initialize the teacher extractor with login details and browser settings.
        
        Args:
            username (str): Username for login
            password (str): Password for login
            headless (bool): Whether to run Chrome in headless mode
            debug (bool): Whether to run in debug mode with pauses
        """
        self.base_url = config.BASE_URL
        self.username = username
        self.password = password
        self.debug = debug
        
        # Setup Chrome options
        self.chrome_options = Options()
        if headless:
            self.chrome_options.add_argument("--headless")
        
        self.chrome_options.add_argument("--window-size=1920,1080")
        self.chrome_options.add_argument("--disable-gpu")
        self.chrome_options.add_argument("--disable-extensions")
        self.chrome_options.add_argument("--no-sandbox")
        self.chrome_options.add_argument("--disable-dev-shm-usage")
        self.chrome_options.add_argument("--lang=ja")
        
        # Initialize the driver
        self.initialize_driver()
        
        # Create output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = f"teacher_data_{timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize statistics
        self.stats = {
            "total_teachers": 0,
            "processed_teachers": 0,
            "successful_extractions": 0,
            "failed_extractions": 0,
            "start_time": time.time(),
            "end_time": None
        }
    
    def initialize_driver(self):
        """Initialize the Chrome WebDriver"""
        try:
            self.driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=self.chrome_options
            )
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("WebDriver initialized")
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            raise
    
    def login(self):
        """Log in to the EN Dance System admin interface"""
        try:
            logger.info("Attempting to log in...")
            self.driver.get(self.base_url)
            
            # Wait for and fill in username
            username_field = self.wait.until(
                EC.presence_of_element_located((By.TAG_NAME, "input"))
            )
            username_field.send_keys(self.username)
            
            # Fill in password
            password_field = self.driver.find_element(By.XPATH, "//input[@type='password']")
            password_field.send_keys(self.password)
            
            # Click login button
            submit_button = self.driver.find_element(
                By.XPATH, "//input[@type='submit'] | //button[text()='ログイン']"
            )
            submit_button.click()
            
            # Wait for login success
            self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//body[contains(., '本部用ツール')]"))
            )
            logger.info("Login successful")
            return True
        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False
    
    def navigate_to_teacher_list(self):
        """Navigate to the teacher list page"""
        try:
            logger.info("Navigating to teacher list page...")
            teacher_list_url = f"{self.base_url}/tool/teacher_main.php"
            self.driver.get(teacher_list_url)
            
            # Wait for the page to load
            self.wait.until(
                EC.presence_of_element_located((By.TAG_NAME, "table"))
            )
            logger.info("Successfully navigated to teacher list page")
            return True
        except Exception as e:
            logger.error(f"Failed to navigate to teacher list page: {e}")
            return False
    
    def get_page_info(self):
        """Get current page number and total records"""
        try:
            # Wait for the page information to be visible
            page_info = self.wait.until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(text(), '件中')]"))
            ).text
            
            # Extract current page and total records
            # Format: "1-20 件中 100 件"
            parts = page_info.split()
            current_range = parts[0].split('-')
            total_records = int(parts[2])
            
            return {
                "current_start": int(current_range[0]),
                "current_end": int(current_range[1]),
                "total_records": total_records
            }
        except Exception as e:
            logger.error(f"Failed to get page information: {e}")
            return None
    
    def navigate_to_page(self, page_number):
        """Navigate to a specific page using the SetStartNo2 function"""
        try:
            # Execute JavaScript to navigate to the page
            script = f"SetStartNo2({(page_number-1)*20})"
            self.driver.execute_script(script)
            
            # Wait for the page to load
            self.wait.until(
                EC.presence_of_element_located((By.TAG_NAME, "table"))
            )
            logger.info(f"Navigated to page {page_number}")
            return True
        except Exception as e:
            logger.error(f"Failed to navigate to page {page_number}: {e}")
            return False
    
    def extract_teacher_list(self):
        """Extract teacher information from the current page"""
        try:
            # Get the table containing teacher information
            table = self.wait.until(
                EC.presence_of_element_located((By.TAG_NAME, "table"))
            )
            
            # Get all rows except the header
            rows = table.find_elements(By.TAG_NAME, "tr")[1:]  # Skip header row
            
            teachers = []
            for row in rows:
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 6:  # Ensure we have all required columns
                    teacher = {
                        "id": cells[0].text.strip(),
                        "public": cells[1].text.strip(),
                        "type": cells[2].text.strip(),
                        "category": cells[3].text.strip(),
                        "name": cells[4].text.strip(),
                        "studio": cells[5].text.strip()
                    }
                    teachers.append(teacher)
            
            return teachers
        except Exception as e:
            logger.error(f"Failed to extract teacher list: {e}")
            return []
    
    def extract_teacher_details(self, teacher):
        """Extract detailed information for a specific teacher"""
        try:
            # Find the row containing the teacher
            row = self.wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//td[contains(text(), '{teacher['id']}')]/..")
                )
            )
            
            # Create directory for teacher data
            teacher_dir = os.path.join(self.output_dir, f"teacher_{teacher['id']}")
            os.makedirs(teacher_dir, exist_ok=True)
            
            # Extract data from each button
            for button_name, config in button_config.BUTTON_CONFIG.items():
                try:
                    # Click the button
                    button = row.find_element(By.XPATH, config["selectors"][0])
                    button.click()
                    
                    # Wait for the page to load
                    time.sleep(1)  # Add small delay for page transition
                    
                    # Extract data based on configuration
                    data = self.extract_page_data(button_name)
                    
                    # Save the data
                    button_dir = os.path.join(teacher_dir, button_name)
                    os.makedirs(button_dir, exist_ok=True)
                    
                    with open(os.path.join(button_dir, f"{button_name}_data.json"), "w", encoding="utf-8") as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    # Go back to the list page
                    back_button = self.wait.until(
                        EC.presence_of_element_located((By.XPATH, config["back_selectors"][0]))
                    )
                    back_button.click()
                    
                    # Wait for the list page to load
                    self.wait.until(
                        EC.presence_of_element_located((By.TAG_NAME, "table"))
                    )
                    
                except Exception as e:
                    logger.error(f"Failed to extract {button_name} data for teacher {teacher['id']}: {e}")
            
            return True
        except Exception as e:
            logger.error(f"Failed to extract details for teacher {teacher['id']}: {e}")
            return False
    
    def extract_page_data(self, button_name):
        """Extract data from the current page based on button configuration"""
        data = {}
        
        # Extract table data if configured
        if button_config.DATA_EXTRACTION_CONFIG[button_name]["tables"]:
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            for i, table in enumerate(tables):
                table_data = []
                rows = table.find_elements(By.TAG_NAME, "tr")
                for row in rows:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if cells:
                        table_data.append([cell.text.strip() for cell in cells])
                data[f"table_{i}"] = table_data
        
        # Extract form data if configured
        if button_config.DATA_EXTRACTION_CONFIG[button_name]["forms"]:
            forms = self.driver.find_elements(By.TAG_NAME, "form")
            for i, form in enumerate(forms):
                form_data = {}
                inputs = form.find_elements(By.TAG_NAME, "input")
                for input_elem in inputs:
                    name = input_elem.get_attribute("name")
                    value = input_elem.get_attribute("value")
                    if name:
                        form_data[name] = value
                data[f"form_{i}"] = form_data
        
        return data
    
    def run_extraction(self):
        """Run the complete extraction process"""
        try:
            # Login
            if not self.login():
                logger.error("Failed to login. Exiting.")
                return False
            
            # Navigate to teacher list
            if not self.navigate_to_teacher_list():
                logger.error("Failed to navigate to teacher list. Exiting.")
                return False
            
            # Get page information
            page_info = self.get_page_info()
            if not page_info:
                logger.error("Failed to get page information. Exiting.")
                return False
            
            total_pages = (page_info["total_records"] + 19) // 20  # 20 records per page
            
            # Process each page
            for page in range(1, total_pages + 1):
                logger.info(f"Processing page {page} of {total_pages}")
                
                # Navigate to the page
                if not self.navigate_to_page(page):
                    logger.error(f"Failed to navigate to page {page}. Skipping.")
                    continue
                
                # Extract teachers from current page
                teachers = self.extract_teacher_list()
                self.stats["total_teachers"] += len(teachers)
                
                # Process each teacher
                for teacher in teachers:
                    logger.info(f"Processing teacher {teacher['id']} - {teacher['name']}")
                    if self.extract_teacher_details(teacher):
                        self.stats["successful_extractions"] += 1
                    else:
                        self.stats["failed_extractions"] += 1
                    self.stats["processed_teachers"] += 1
            
            # Save final statistics
            self.stats["end_time"] = time.time()
            with open(os.path.join(self.output_dir, "extraction_stats.json"), "w", encoding="utf-8") as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
            
            logger.info("Extraction completed successfully")
            return True
        except Exception as e:
            logger.error(f"Extraction failed: {e}")
            return False
        finally:
            self.driver.quit()

def main():
    parser = argparse.ArgumentParser(description="EN Dance System Teacher Information Extractor")
    parser.add_argument("--username", help="Admin username")
    parser.add_argument("--password", help="Admin password")
    parser.add_argument("--headless", action="store_true", help="Run in headless mode")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    # Get username and password if not provided
    username = args.username or input("Enter admin username: ")
    password = args.password or getpass("Enter admin password: ")
    
    # Create and run the extractor
    extractor = TeacherExtractor(username, password, args.headless, args.debug)
    extractor.run_extraction()

if __name__ == "__main__":
    main() 