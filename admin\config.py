"""
Configuration file for the EN Dance System crawler.
"""

# Base URL of the management system
BASE_URL = "https://www.en-system.net/admin"  # Removed trailing slash to prevent double slash

# Login page configuration
LOGIN_CONFIG = {
    # Using simpler selectors that are more likely to work
    "username_selector": {"by": "tag", "value": "input"},
    "password_selector": {"by": "xpath", "value": "//input[@type='password']"},
    "submit_selector": {"by": "xpath", "value": "//input[@type='submit'] | //button[text()='ログイン']"},
    "success_indicator": {"by": "xpath", "value": "//body[contains(., '本部用ツール')]"}
}

# Pages to crawl - updated with actual URLs from the system
PAGES_TO_CRAWL = [
    # Auto-detected menu items - updated on 2025-03-24 08:04:57

    # 本部管理
    {
        "name": "グループ管理",  # Menu item name
        "url": "/tool/group_main.php",
        "filename": "group_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "教室管理",  # Menu item name
        "url": "/tool/room_main.php",
        "filename": "room_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "対象クラス管理",  # Menu item name
        "url": "/tool/age_main.php",
        "filename": "age_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "料金コース情報登録",  # Menu item name
        "url": "/tool/course_main.php",
        "filename": "course_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "短期利用コース情報登録",  # Menu item name
        "url": "/tool/short_course_main.php",
        "filename": "short_course_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "カテゴリ管理",  # Menu item name
        "url": "/tool/category_main.php",
        "filename": "category_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "レッスン登録",  # Menu item name
        "url": "/tool/lesson_main.php",
        "filename": "lesson_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "商品カテゴリ登録",  # Menu item name
        "url": "/tool/shohin_cate_main.php",
        "filename": "shohin_cate_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "商品ﾊﾞﾘｴｰｼｮﾝ管理",  # Menu item name
        "url": "/tool/shohin_vari_main.php",
        "filename": "shohin_vari_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "商品登録",  # Menu item name
        "url": "/tool/shohin_main.php",
        "filename": "shohin_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "付与ポイント管理",  # Menu item name
        "url": "/tool/point_search.php",
        "filename": "point_search.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "指定クラス出力",  # Menu item name
        "url": "/tool/shitei_lesson.php",
        "filename": "shitei_lesson.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },

    # スタッフ管理
    {
        "name": "スタッフ登録",  # Menu item name
        "url": "/tool/teacher_main.php",
        "filename": "teacher_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "ｲﾝｽﾄﾗｸﾀｰ別集客一覧",  # Menu item name
        "url": "/tool/syukyaku_ichiran.php",
        "filename": "syukyaku_ichiran.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "ｲﾝｽﾄﾗｸﾀｰ報酬",  # Menu item name
        "url": "/tool/kyuyo_ichiran.php",
        "filename": "kyuyo_ichiran.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "報酬振込データ作成",  # Menu item name
        "url": "/tool/kyuyo_createdata.php",
        "filename": "kyuyo_createdata.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "ｲﾝｽﾄﾗｸﾀｰ日払い報酬",  # Menu item name
        "url": "/tool/kyuyo_ichiran_sub.php",
        "filename": "kyuyo_ichiran_sub.csv",
        "table_selector": "table",
        "skip": True,  # Skip this page due to table structure issues
        "issue": "Inconsistent table structure - columns mismatch (7 vs 13)",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "ｲﾝｽﾄﾗｸﾀｰ報酬台帳",  # Menu item name
        "url": "/tool/kyuyo_daicho.php",
        "filename": "kyuyo_daicho.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "My Pageお知らせ登録",  # Menu item name
        "url": "/tool/teacher_oshirase_main.php",
        "filename": "teacher_oshirase_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },

    # スケジュール管理
    {
        "name": "ｲﾝｽﾄﾗｸﾀｰｽｹｼﾞｭｰﾙ",  # Menu item name
        "url": "/tool/schedule_teacher_main.php",
        "filename": "schedule_teacher_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "教室基本スケジュール",  # Menu item name
        "url": "/tool/room_sche_base_main.php",
        "filename": "room_sche_base_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "教室スケジュール",  # Menu item name
        "url": "/tool/room_sche_group_main.php",
        "filename": "room_sche_group_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "（旧）教室スケジュール",  # Menu item name
        "url": "/tool/room_sche_main.php",
        "filename": "room_sche_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },

    # 教室連絡
    {
        "name": "お知らせ登録",  # Menu item name
        "url": "/tool/oshirase_main.php",
        "filename": "oshirase_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "目標登録",  # Menu item name
        "url": "/tool/mokuhyo_main.php",
        "filename": "mokuhyo_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "スタッフ日報",  # Menu item name
        "url": "/tool/nippo_staff_h.php",
        "filename": "nippo_staff_h.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },

    # 月謝引落し管理
    {
        "name": "月謝引落しデータ作成",  # Menu item name
        "url": "/tool/bank_hikiotoshi.php",
        "filename": "bank_hikiotoshi.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "月謝引落し結果取込",  # Menu item name
        "url": "/tool/bank_hiki_torikomi.php",
        "filename": "bank_hiki_torikomi.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "月謝引落し結果詳細",  # Menu item name
        "url": "/tool/bank_hiki_syosai.php",
        "filename": "bank_hiki_syosai.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "未納最終勧告書",  # Menu item name
        "url": "/tool/saisyu_tukoku.php",
        "filename": "saisyu_tukoku.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "フィットペイメント管理",  # Menu item name
        "url": "/tool/daiibensai_kanri.php",
        "filename": "daiibensai_kanri.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "月謝クレジットカード引落し結果詳細",  # Menu item name
        "url": "/tool/credit_hiki_syosai.php",
        "filename": "credit_hiki_syosai.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },

    # 各種集計
    {
        "name": "全体売上集計",  # Menu item name
        "url": "/tool/syukei_uriage_h.php",
        "filename": "syukei_uriage_h.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "コース別売上集計",  # Menu item name
        "url": "/tool/syukei_course_h.php",
        "filename": "syukei_course_h.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "商品別売上集計",  # Menu item name
        "url": "/tool/syukei_shohin_h.php",
        "filename": "syukei_shohin_h.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "会員数集計",  # Menu item name
        "url": "/tool/syukei_user_h.php",
        "filename": "syukei_user_h.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "報酬集計",  # Menu item name
        "url": "/tool/syukei_kyuyo_h.php",
        "filename": "syukei_kyuyo_h.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "紹介集計",  # Menu item name
        "url": "/tool/coupon_total.php",
        "filename": "coupon_total.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "AirPay決済集計",  # Menu item name
        "url": "/tool/syukei_airpay.php",
        "filename": "syukei_airpay.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "GMO決済集計",  # Menu item name
        "url": "/tool/syukei_gmo.php",
        "filename": "syukei_gmo.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },

    # ホームページ管理
    {
        "name": "ｲﾝｽﾄﾗｸﾀｰ表示設定",  # Menu item name
        "url": "/tool/hp_teacher_main.php",
        "filename": "hp_teacher_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "ｵﾝﾗｲﾝﾚｯｽﾝ登録",  # Menu item name
        "url": "/tool/online_main.php",
        "filename": "online_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
    {
        "name": "ｲﾍﾞﾝﾄ・ﾜｰｸｼｮｯﾌﾟ登録",  # Menu item name
        "url": "/tool/event_workshop_main.php",
        "filename": "event_workshop_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },

    # 管理機能
    {
        "name": "月次確定取消",  # Menu item name
        "url": "/tool/torikeshi_main.php",
        "filename": "torikeshi_main.csv",
        "table_selector": "table",
        "pagination": {
            "next_button": {"by": "xpath", "value": "//a[contains(text(), '次へ')]"}
        }
    },
]

# Selenium WebDriver configuration
WEBDRIVER_CONFIG = {
    "headless": False,  # Set to True for production
    "window_size": "1920,1080",
    "disable_gpu": True,
    "disable_extensions": True,
    "no_sandbox": True,
    "disable_dev_shm_usage": True,
    "wait_timeout": 20  # Increased timeout
}

# Output configuration
OUTPUT_CONFIG = {
    "csv_encoding": "utf-8-sig",  # Important for Japanese characters
    "include_index": False
}

# Advanced options for the crawler
ADVANCED_OPTIONS = {
    "auto_detect_menu": True,
    "save_detected_menu": True,
    "manual_login": True,
    "page_load_delay": 2,
    
    # Human-like behavior options
    "human_like_delays": True,        # Add random delays to simulate human behavior
    "page_load_pause": (1, 3),        # Random pause range after page load (min, max seconds)
    
    # Resource management options
    "max_retries": 5,                 # Maximum number of retries for finding elements
    "max_cpu_percent": 80,            # Maximum CPU usage before pausing
    "max_session_duration": 3600,     # Maximum session duration in seconds (1 hour)
    "idle_timeout": 300,              # Maximum time to wait if no activity (5 minutes)
    
    # Browser crash handling
    "max_browser_crashes": 3,         # Maximum number of browser crashes before giving up
    "browser_restart_delay": 5,       # Seconds to wait before restarting browser
    
    # Page content handling options
    "extract_text_when_no_table": True,  # Extract page text content when no table is found
    "skip_form_pages": True              # Skip pages that appear to be forms rather than data tables
} 